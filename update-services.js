#!/usr/bin/env node

/**
 * 批量更新服务到新架构的脚本
 */

const fs = require('fs');
const path = require('path');

const services = ['economy', 'hero', 'match', 'social'];

function updateMainTs(serviceName) {
  const mainPath = `apps/${serviceName}/src/main.ts`;
  
  if (!fs.existsSync(mainPath)) {
    console.log(`⚠️ ${mainPath} 不存在，跳过`);
    return;
  }

  let content = fs.readFileSync(mainPath, 'utf8');
  
  // 更新导入
  content = content.replace(
    /import { PortManager } from '@port-manager';/g,
    "import { ServiceConfigurationBuilder } from '@libs/service-mesh';"
  );
  
  // 更新bootstrap函数开头
  const oldBootstrapStart = /async function bootstrap\(\) \{[\s\S]*?const environment = configService\.get\('NODE_ENV', 'development'\);/;
  const newBootstrapStart = `async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // 🎯 1. 创建应用实例
  const app = await NestFactory.create(AppModule);

  // 2. 获取配置服务并计算配置（新架构）
  const configService = app.get(ConfigService);
  const serviceConfig = await ServiceConfigurationBuilder.build('${serviceName}', configService);

  logger.log(\`🎯 服务配置获取完成:\`);
  logger.log(\`   📋 服务名称: \${serviceConfig.serviceName}\`);
  logger.log(\`   🏰 区服ID: \${serviceConfig.serverId}\`);
  logger.log(\`   🔢 实例序号: \${serviceConfig.instanceIndex}\`);
  logger.log(\`   🌐 监听地址: \${serviceConfig.host}:\${serviceConfig.port}\`);
  logger.log(\`   🔧 端口计算: \${serviceConfig.portCalculated ? '动态计算' : '固定配置'}\`);
  logger.log(\`   🌍 运行环境: \${serviceConfig.environment}\`);`;

  content = content.replace(oldBootstrapStart, newBootstrapStart);
  
  // 更新端口引用
  content = content.replace(/\${port}/g, '${serviceConfig.port}');
  content = content.replace(/port,/g, 'serviceConfig.port,');
  content = content.replace(/environment/g, 'serviceConfig.environment');
  
  // 更新启动监听
  content = content.replace(
    /await app\.listen\(port, '0\.0\.0\.0'\);/g,
    'await app.listen(serviceConfig.port, serviceConfig.host);'
  );
  
  fs.writeFileSync(mainPath, content);
  console.log(`✅ 更新 ${mainPath}`);
}

function updateAppModule(serviceName) {
  const modulePath = `apps/${serviceName}/src/app.module.ts`;
  
  if (!fs.existsSync(modulePath)) {
    console.log(`⚠️ ${modulePath} 不存在，跳过`);
    return;
  }

  let content = fs.readFileSync(modulePath, 'utf8');
  
  // 确保ServiceMeshModule.register使用简洁形式
  const serviceRegisterRegex = new RegExp(`ServiceMeshModule\\.register\\('${serviceName}'[^)]*\\)`, 'g');
  content = content.replace(serviceRegisterRegex, `ServiceMeshModule.register('${serviceName}')`);
  
  fs.writeFileSync(modulePath, content);
  console.log(`✅ 更新 ${modulePath}`);
}

// 执行更新
console.log('🚀 开始批量更新服务到新架构...');

services.forEach(serviceName => {
  console.log(`\n📋 更新 ${serviceName} 服务:`);
  updateMainTs(serviceName);
  updateAppModule(serviceName);
});

console.log('\n🎉 批量更新完成！');
