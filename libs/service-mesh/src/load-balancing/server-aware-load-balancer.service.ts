import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServerAwareRegistryService } from '../registry/server-aware-registry.service';
import { ServerAwareServiceInstance } from '../interfaces/service-instance.interfaces';
import { LoadBalancingUtils } from './load-balancing.utils';
import { BaseLoadBalancerService, BaseLoadBalancingStrategy } from './base-load-balancer.service';

/**
 * 区服负载均衡策略接口
 */
export interface ServerAwareLoadBalancingStrategy extends BaseLoadBalancingStrategy<ServerAwareServiceInstance> {
  selectInstance(instances: ServerAwareServiceInstance[], context?: LoadBalancingContext): ServerAwareServiceInstance | null;
}

/**
 * 负载均衡上下文
 */
export interface LoadBalancingContext {
  ip?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  [key: string]: any;
}

/**
 * 区服感知的负载均衡器 - 纯算法实现
 *
 * 🎯 核心职责：
 * - ✅ 负载均衡策略实现（继承通用策略 + 区服特定策略）
 * - ✅ 从实例数组中选择最佳实例
 * - ✅ 支持多种负载均衡算法
 * - ❌ 不获取实例（避免循环依赖）
 * - ❌ 不管理连接数（保持纯函数特性）
 *
 * 🏗️ 架构原则：
 * - 单一职责：只做负载均衡算法
 * - 无副作用：不修改实例状态
 * - 无依赖：不依赖Registry服务
 * - 代码复用：继承基类获得通用策略
 */
@Injectable()
export class ServerAwareLoadBalancerService extends BaseLoadBalancerService<
  ServerAwareServiceInstance,
  ServerAwareLoadBalancingStrategy
> {

  constructor(configService: ConfigService) {
    super(
      configService,
      'gateway.loadBalancer.defaultStrategy',
      'intelligent' // 默认使用智能策略
    );
  }

  /**
   * 🎯 从实例列表中选择最佳实例 - 重写基类方法
   *
   * @param instances 候选实例列表
   * @param options 负载均衡选项
   * @returns 选中的实例，如果没有可用实例则返回null
   */
  async selectInstance(
    instances: ServerAwareServiceInstance[],
    options: { strategy?: string; context?: LoadBalancingContext } = {}
  ): Promise<ServerAwareServiceInstance | null> {
    const { strategy = this.defaultStrategy, context = {} } = options;

    // 过滤健康实例
    const healthyInstances = LoadBalancingUtils.filterHealthyInstances(instances);

    if (healthyInstances.length === 0) {
      this.logger.warn(`❌ 没有可用的健康实例`);
      return null;
    }

    // 增强上下文信息
    const enhancedContext = {
      ...context,
      counterKey: context.counterKey || `${context.serviceName || 'default'}-${context.serverId || 'default'}`
    };

    // 获取负载均衡策略
    const loadBalancer = this.strategies.get(strategy);
    if (!loadBalancer) {
      this.logger.warn(`⚠️ 未知的负载均衡策略: ${strategy}，使用默认策略: ${this.defaultStrategy}`);
      return this.selectInstance(healthyInstances, { strategy: this.defaultStrategy, context: enhancedContext });
    }

    // 选择实例 - 纯算法，无副作用
    const selectedInstance = loadBalancer.selectInstance(healthyInstances, enhancedContext);

    if (selectedInstance) {
      this.logger.debug(`🎯 选择实例: ${selectedInstance.instanceName} 策略=${strategy}`);
    }

    return selectedInstance;
  }

  /**
   * 🔧 初始化区服特定策略 - 实现基类抽象方法
   */
  protected initializeSpecificStrategies(): void {
    // 智能策略 - 区服服务的默认策略
    this.strategies.set('intelligent', this.createStrategy('intelligent', (instances: ServerAwareServiceInstance[], context?: LoadBalancingContext) => {
      return this.intelligentSelect(instances, context);
    }));

    // 连接优先策略 - 区服特有
    this.strategies.set('connection-priority', this.createStrategy('connection-priority', (instances: ServerAwareServiceInstance[]) => {
      const sorted = LoadBalancingUtils.sortByConnections(instances, true);
      return sorted[0] || null;
    }));

    // 响应时间优先策略 - 区服特有
    this.strategies.set('response-priority', this.createStrategy('response-priority', (instances: ServerAwareServiceInstance[]) => {
      const sorted = LoadBalancingUtils.sortByResponseTime(instances, true);
      return sorted[0] || null;
    }));

    this.logger.log(`🏰 区服特定策略初始化完成，总策略数: ${this.strategies.size}`);
  }


  // ==================== 私有方法 ====================

  /**
   * 🏭 策略工厂方法 - 实现基类抽象方法
   */
  protected createStrategy(name: string, selectFn: (instances: ServerAwareServiceInstance[], context?: any) => ServerAwareServiceInstance | null): ServerAwareLoadBalancingStrategy {
    return {
      name,
      selectInstance: selectFn
    };
  }

  /**
   * 📊 重写智能排序规则 - 区服特定优化
   */
  protected getIntelligentSortRules(context?: any): Array<{
    key: any;
    ascending?: boolean;
    weight?: number;
  }> {
    return [
      { key: 'healthy', ascending: false, weight: 1000 },        // 健康度优先
      { key: 'connections', ascending: true, weight: 100 },      // 少连接优先（区服特有）
      { key: 'responseTime', ascending: true, weight: 50 },      // 快响应优先（区服特有）
      { key: 'weight', ascending: false, weight: 10 },           // 权重优先
      { key: (instance: any) => new Date(instance.registeredAt).getTime(), ascending: false, weight: 5 }, // 最新注册优先
    ];
  }
}
