import { Injectable, Optional, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoadBalancingUtils } from './load-balancing.utils';
import { GlobalServiceInstance } from '../interfaces/service-instance.interfaces';
import { BaseLoadBalancerService, BaseLoadBalancingStrategy } from './base-load-balancer.service';

/**
 * 全局服务负载均衡策略接口
 */
export interface GlobalLoadBalancingStrategy extends BaseLoadBalancingStrategy<StoredGlobalServiceInstance> {
  selectInstance(instances: StoredGlobalServiceInstance[], context?: GlobalLoadBalancingContext): StoredGlobalServiceInstance | null;
}

/**
 * 全局服务负载均衡上下文
 */
export interface GlobalLoadBalancingContext {
  ip?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  serviceName?: string;
  portChecker?: (instance: StoredGlobalServiceInstance, serviceName: string) => boolean;
  [key: string]: any;
}

/**
 * 全局服务专用负载均衡器
 *
 * 特点：
 * - 复用现有的StoredGlobalServiceInstance接口
 * - 支持端口正确性优先级
 * - 支持注册时间优先级 (最新优先)
 * - 智能多维度排序
 * - 继承通用策略 + 全局服务特定策略
 */
@Injectable()
export class GlobalServiceLoadBalancerService extends BaseLoadBalancerService<
  StoredGlobalServiceInstance,
  GlobalLoadBalancingStrategy
> {

  constructor(
    configService: ConfigService,
  ) {
    super(
      configService,
      'gateway.globalLoadBalancer.defaultStrategy',
      'intelligent' // 默认使用智能策略
    );
  }

  /**
   * 🎯 从实例列表中选择 - 纯算法实现
   */
  async selectInstance(
    instances: StoredGlobalServiceInstance[],
    strategy: string = this.defaultStrategy,
    context: GlobalLoadBalancingContext = {}
  ): Promise<StoredGlobalServiceInstance | null> {
    // 过滤健康实例
    const healthyInstances = LoadBalancingUtils.filterHealthyInstances(instances);

    if (healthyInstances.length === 0) {
      this.logger.warn(`❌ 没有可用的健康全局服务实例`);
      return null;
    }

    // 增强上下文信息
    const enhancedContext = {
      ...context,
      counterKey: context.serviceName || 'global' // 全局级别的计数器键
    };

    // 获取负载均衡策略
    const loadBalancer = this.strategies.get(strategy);
    if (!loadBalancer) {
      this.logger.warn(`⚠️ 未知的全局负载均衡策略: ${strategy}，使用默认策略: ${this.defaultStrategy}`);
      return this.selectInstance(healthyInstances, this.defaultStrategy, enhancedContext);
    }

    // 选择实例
    const selectedInstance = loadBalancer.selectInstance(healthyInstances, enhancedContext);

    if (selectedInstance) {
      this.logger.debug(`🎯 选择全局服务实例: ${selectedInstance.id} (${selectedInstance.serviceName}) 策略=${strategy}`);
    }

    return selectedInstance;
  }

  /**
   * 🏭 策略工厂方法 - 实现基类抽象方法
   */
  protected createStrategy(name: string, selectFn: (instances: StoredGlobalServiceInstance[], context?: any) => StoredGlobalServiceInstance | null): GlobalLoadBalancingStrategy {
    return {
      name,
      selectInstance: selectFn
    };
  }

  /**
   * 🔧 初始化全局服务特定策略 - 实现基类抽象方法
   */
  protected initializeSpecificStrategies(): void {
    // 智能策略 - 全局服务的默认策略
    this.strategies.set('intelligent', this.createStrategy('intelligent', (instances: StoredGlobalServiceInstance[], context?: GlobalLoadBalancingContext) => {
      return this.intelligentSelect(instances, context);
    }));

    // 端口优先策略 - 全局服务特有
    this.strategies.set('port-priority', this.createStrategy('port-priority', (instances: StoredGlobalServiceInstance[], context?: GlobalLoadBalancingContext) => {
      return this.portPrioritySelect(instances, context);
    }));

    // 时间优先策略 (最新注册优先) - 全局服务特有
    this.strategies.set('latest-first', this.createStrategy('latest-first', (instances: StoredGlobalServiceInstance[]) => {
      const sorted = LoadBalancingUtils.sortByRegistrationTime(instances, false);
      return sorted[0] || null;
    }));

    // 心跳优先策略 (最近心跳优先) - 全局服务特有
    this.strategies.set('heartbeat-priority', this.createStrategy('heartbeat-priority', (instances: StoredGlobalServiceInstance[]) => {
      const sorted = LoadBalancingUtils.sortByHeartbeat(instances, false);
      return sorted[0] || null;
    }));

    this.logger.log(`🌍 全局特定策略初始化完成，总策略数: ${this.strategies.size}`);
  }

  /**
   * 📊 重写智能排序规则 - 全局服务特定优化
   */
  protected getIntelligentSortRules(context?: any): Array<{
    key: keyof StoredGlobalServiceInstance | ((instance: StoredGlobalServiceInstance) => any);
    ascending?: boolean;
    weight?: number;
  }> {
    const rules: Array<{
      key: keyof StoredGlobalServiceInstance | ((instance: StoredGlobalServiceInstance) => any);
      ascending?: boolean;
      weight?: number;
    }> = [
      { key: 'healthy', ascending: false, weight: 1000 },        // 健康度优先
    ];

    // 端口正确性优先（如果有端口检查器）
    if (context?.portChecker && context?.serviceName) {
      rules.push({
        key: (instance: StoredGlobalServiceInstance) => context.portChecker(instance, context.serviceName),
        ascending: false,
        weight: 100
      });
    }

    rules.push(
      { key: (instance: StoredGlobalServiceInstance) => new Date(instance.registeredAt).getTime(), ascending: false, weight: 50 }, // 最新注册优先
      { key: 'weight', ascending: false, weight: 10 },           // 权重优先
      { key: (instance: StoredGlobalServiceInstance) => new Date(instance.lastHeartbeat).getTime(), ascending: false, weight: 5 }, // 最新心跳优先
    );

    return rules;
  }

  /**
   * 🔍 端口优先选择算法
   */
  private portPrioritySelect(
    instances: StoredGlobalServiceInstance[],
    context?: GlobalLoadBalancingContext
  ): StoredGlobalServiceInstance | null {
    if (instances.length === 0) return null;

    // 如果有端口检查器，优先选择端口正确的实例
    if (context?.portChecker && context?.serviceName) {
      const correctPortInstances = instances.filter(instance => 
        context.portChecker!(instance, context.serviceName!)
      );
      
      if (correctPortInstances.length > 0) {
        // 在端口正确的实例中按注册时间选择最新的
        const sorted = LoadBalancingUtils.sortByRegistrationTime(correctPortInstances, false);
        return sorted[0];
      }
    }

    // 如果没有端口正确的实例，按注册时间选择最新的
    const sorted = LoadBalancingUtils.sortByRegistrationTime(instances, false);
    return sorted[0];
  }
}
