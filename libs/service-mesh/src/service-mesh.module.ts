import { Module, Global, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RedisModule } from '@common/redis';

// 🚀 导入MicroserviceKit作为纯调用库
import { MicroserviceKitModule, MicroserviceClientService } from '@common/microservice-kit';

// 核心服务
import { ServerAwareRegistryService } from './registry/server-aware-registry.service';
import { ServerAwareLoadBalancerService } from './load-balancing/server-aware-load-balancer.service';

// 实例管理服务
import { InstanceLifecycleService } from './lifecycle/instance-lifecycle.service';
import { ServerConfigGeneratorService } from './orchestration/server-config-generator.service';
import { ContainerOrchestrationService } from './orchestration/container-orchestration.service';

// 自动注册服务
import { ServiceAutoRegistrationService } from './registry/service-auto-registration.service';

// 统一服务发现
import { UnifiedServiceDiscoveryService } from './discovery/unified-service-discovery.service';

// 全局服务注册
import { GlobalServiceRegistryService } from './registry/global-service-registry.service';
import { GlobalServiceAutoRegistrationService } from './registry/global-service-auto-registration.service';
import { GlobalServiceLoadBalancerService } from './load-balancing/global-service-load-balancer.service';

// 🚀 统一架构相关导入
import { ServiceOptions, ServiceRole, AutoConfig } from './interfaces/service-options.interface';
import { ServiceInferenceUtil } from './utils/service-inference.util';
import { ServiceConfiguration } from './interfaces/service-instance.interfaces';

/**
 * 服务网格模块 (Service Mesh Module)
 *
 * 提供完整的微服务基础设施，包括服务发现、注册、负载均衡和治理：
 * - 全局服务：跨区服共享，如Auth、Payment
 * - 区服服务：区服隔离，如Character、Hero
 * - 统一服务发现：智能识别服务类型
 * - 负载均衡：多种策略支持
 * - 实例管理：生命周期管理和健康检查
 *
 * 核心特性：
 * - 分层清晰：网关层、全局服务层、区服服务层
 * - 智能路由：自动选择合适的服务发现方式
 * - 负载均衡：全局和区服服务都支持负载均衡
 * - 零侵入性：业务代码最小化修改
 * - 容器编排：支持动态扩缩容和配置生成
 *
 * 使用模式（借鉴 microservice-kit 设计）：
 * - forRoot(): 网关模式，提供统一服务发现
 * - forGlobal(): 全局服务模式，如Auth
 * - forServer(): 区服服务模式，如Character
 */
@Module({})
export class ServiceMeshModule {

  /**
   * 🚀 配置缓存 - 避免重复计算，提升性能
   */
  private static configCache = new Map<string, DynamicModule>();

  /**
   * 🎯 优化的服务注册方法 - 支持预计算配置
   *
   * 核心特性：
   * - ✅ 支持预计算配置，避免重复计算
   * - ✅ 自动推断服务角色（client/global/server）
   * - ✅ 自动分析依赖关系
   * - ✅ 自动获取环境配置
   * - ✅ 自动生成服务白名单
   *
   * @param serviceName 服务名称
   * @param options 配置选项，推荐提供 preCalculatedConfig
   * @returns 动态模块配置
   */
  static register(serviceName: string, options?: ServiceOptions): DynamicModule {
    // 🔍 输入验证
    if (!serviceName || typeof serviceName !== 'string') {
      throw new Error(`🚨 [ServiceMesh] 无效的服务名称: ${serviceName}`);
    }

    try {
      // 🚀 检查缓存
      const cacheKey = this.generateCacheKey(serviceName, options);
      if (this.configCache.has(cacheKey)) {
        console.log(`📦 [ServiceMesh] 使用缓存配置: ${serviceName}`);
        return this.configCache.get(cacheKey)!;
      }

      // 🧠 智能推断所有配置
      const role = options?.role ?? ServiceInferenceUtil.inferServiceRole(serviceName);
      const services = options?.services ?? ServiceInferenceUtil.inferServiceDependencies(serviceName);
      const autoConfig = ServiceInferenceUtil.getAutoConfig(serviceName);
      const serverId = options?.serverId ?? autoConfig.serverId;

      console.log(`🚀 [ServiceMesh] 注册服务: ${serviceName}`);
      console.log(`   📋 角色: ${role} (${role === 'client' ? '纯客户端' : role === 'global' ? '全局服务' : '区服服务'})`);
      console.log(`   🔗 依赖: [${services.join(', ')}] (${services.length}个服务)`);
      console.log(`   🏷️  区服: ${serverId || 'N/A'}`);
      console.log(`   ⚙️  配置: 自动推断${options ? ' + 自定义覆盖' : ''}`);

      // 🎯 根据角色创建模块配置
      let moduleConfig: DynamicModule;
      switch (role) {
        case 'client':
          moduleConfig = this.createClientModule(serviceName, services, options);
          break;
        case 'global':
          moduleConfig = this.createGlobalModule(serviceName, autoConfig, options);
          break;
        case 'server':
          moduleConfig = this.createServerModule(serviceName, serverId, services, autoConfig, options);
          break;
        default:
          throw new Error(`🚨 未知的服务角色: ${role}`);
      }

      // 🚀 缓存配置
      this.configCache.set(cacheKey, moduleConfig);
      console.log(`✅ [ServiceMesh] 服务注册完成: ${serviceName}`);

      return moduleConfig;
    } catch (error) {
      console.error(`❌ [ServiceMesh] 服务注册失败: ${serviceName}`, error);
      throw error;
    }
  }

  /**
   * 创建客户端模块配置
   * 用于Gateway等纯客户端服务
   *
   * 特点：
   * - 只调用其他服务，不提供服务
   * - 需要统一服务发现能力
   * - 支持全局服务和区服服务调用
   * - 自动生成服务白名单验证
   */
  private static createClientModule(
    serviceName: string,
    services: string[],
    options?: ServiceOptions
  ): DynamicModule {
    return {
      module: ServiceMeshModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(), // 🔧 添加EventEmitter支持
        // 🚀 集成MicroserviceKit作为纯调用库
        MicroserviceKitModule.forClient({
          services: services as any[],
        }),
      ],
      providers: [
        UnifiedServiceDiscoveryService,
        GlobalServiceRegistryService,
        GlobalServiceLoadBalancerService,
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        // 🔧 服务白名单验证器
        {
          provide: 'SERVICE_WHITELIST_VALIDATOR',
          useFactory: () => ({
            isServiceAllowed: (targetService: string) => {
              const allowed = services.includes(targetService);
              if (!allowed) {
                console.warn(`🚨 服务 ${targetService} 不在白名单中: [${services.join(', ')}]`);
              }
              return allowed;
            },
            getAllowedServices: () => [...services],
          }),
        },
      ],
      exports: [
        MicroserviceKitModule, // 🚀 重新导出MicroserviceKitModule
        UnifiedServiceDiscoveryService,
        ServerAwareRegistryService, // 🔧 导出给MessageRouterService使用
        ServerAwareLoadBalancerService, // 🔧 导出给MessageRouterService使用
        GlobalServiceLoadBalancerService,
        'SERVICE_WHITELIST_VALIDATOR',
      ],
    };
  }

  /**
   * 创建全局服务模块配置
   * 用于Auth、Payment等全局服务
   *
   * 特点：
   * - 跨区服共享，全局唯一实例
   * - 通常不依赖其他服务（除非特殊需求）
   * - 被所有区服服务调用
   * - 不需要区服感知功能
   */
  private static createGlobalModule(
    serviceName: string,
    autoConfig: AutoConfig,
    options?: ServiceOptions
  ): DynamicModule {
    const services = options?.services ?? [];

    return {
      module: ServiceMeshModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(), // 🔧 添加EventEmitter支持
        // 全局服务如果需要调用其他服务，通过options.services指定
        ...(services.length ? [
          MicroserviceKitModule.forClient({
            services: services as any[],
            enableServerAware: false, // 全局服务调用时不需要区服感知
            enableTraditionalFallback: options?.enableTraditionalFallback ?? false,
          })
        ] : []),
      ],
      providers: [
        GlobalServiceRegistryService,
        GlobalServiceLoadBalancerService, // 🔧 修复：添加缺失的GlobalServiceLoadBalancerService
        {
          provide: 'GLOBAL_SERVICE_CONFIG',
          useValue: {
            serviceName,
            weight: options?.weight ?? 1,
            metadata: {
              ...autoConfig,
              serviceType: 'global',
              features: ServiceInferenceUtil.inferServiceFeatures(serviceName),
              ...options?.metadata,
            },
            healthCheckPath: options?.healthCheckPath ?? '/health',
            autoRegister: options?.autoRegister ?? true,
          },
        },
        GlobalServiceAutoRegistrationService,
        // 🔧 服务白名单验证器（如果有依赖）
        ...(services.length ? [{
          provide: 'SERVICE_WHITELIST_VALIDATOR',
          useFactory: () => ({
            isServiceAllowed: (targetService: string) => services.includes(targetService),
            getAllowedServices: () => [...services],
          }),
        }] : []),
      ],
      exports: [
        GlobalServiceRegistryService,
        GlobalServiceAutoRegistrationService,
        ...(services.length ? [MicroserviceKitModule, 'SERVICE_WHITELIST_VALIDATOR'] : []),
      ],
    };
  }

  /**
   * 创建区服服务模块配置
   * 用于Character、Hero等区服服务
   *
   * 特点：
   * - 区服隔离，每个区服独立实例
   * - 既提供服务又调用其他服务
   * - 支持负载均衡和实例管理
   * - 自动注册到服务注册中心
   * - 强制服务白名单验证
   */
  private static createServerModule(
    serviceName: string,
    serverId: string,
    services: string[],
    autoConfig: AutoConfig,
    options?: ServiceOptions
  ): DynamicModule {
    return {
      module: ServiceMeshModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(),
        // 🚀 集成MicroserviceKit提供调用能力
        MicroserviceKitModule.forClient({
          services: services as any[],
          enableServerAware: true,
          enableTraditionalFallback: options?.enableTraditionalFallback ?? false,
        }),
      ],
      providers: [
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        {
          provide: 'SERVICE_REGISTRATION_CONFIG',
          useValue: {
            serviceName,
            serverId,
            autoRegister: options?.autoRegister ?? true,
            weight: options?.weight ?? 1,
            metadata: {
              ...autoConfig,
              serviceType: 'server',
              features: ServiceInferenceUtil.inferServiceFeatures(serviceName),
              ...options?.metadata,
            },
            healthCheckPath: options?.healthCheckPath ?? '/health',
            allowedServices: services, // 🔧 服务白名单
            preCalculatedConfig: options?.preCalculatedConfig, // 🎯 传递预计算配置
          },
        },
        ServiceAutoRegistrationService,
        // 🔧 服务白名单验证器
        {
          provide: 'SERVICE_WHITELIST_VALIDATOR',
          useFactory: () => ({
            isServiceAllowed: (targetService: string) => {
              const allowed = services.includes(targetService);
              if (!allowed) {
                console.warn(`🚨 服务 ${targetService} 不在白名单中: [${services.join(', ')}]`);
              }
              return allowed;
            },
            getAllowedServices: () => [...services],
            addAllowedService: (service: string) => {
              if (!services.includes(service)) {
                services.push(service);
                console.log(`✅ 已添加 ${service} 到服务白名单`);
              }
            },
            removeAllowedService: (service: string) => {
              const index = services.indexOf(service);
              if (index > -1) {
                services.splice(index, 1);
                console.log(`❌ 已从服务白名单移除 ${service}`);
              }
            },
          }),
        },
      ],
      exports: [
        MicroserviceKitModule, // 🚀 重新导出MicroserviceKitModule
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        ServiceAutoRegistrationService,
        'SERVICE_WHITELIST_VALIDATOR',
      ],
    };
  }

  /**
   * 🔑 生成缓存键
   * 基于服务名称和选项生成唯一的缓存键
   */
  private static generateCacheKey(serviceName: string, options?: ServiceOptions): string {
    const optionsHash = options ? JSON.stringify(options) : 'default';
    return `${serviceName}:${optionsHash}`;
  }

  /**
   * 🧹 清理缓存
   * 用于测试或重新配置时清理缓存
   */
  static clearCache(): void {
    this.configCache.clear();
    console.log('🧹 [ServiceMesh] 配置缓存已清理');
  }

  /**
   * 📊 获取缓存统计
   * 用于监控和调试
   */
  static getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.configCache.size,
      keys: Array.from(this.configCache.keys()),
    };
  }

}