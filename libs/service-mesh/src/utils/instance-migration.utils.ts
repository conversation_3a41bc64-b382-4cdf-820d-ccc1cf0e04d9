/**
 * 服务实例迁移工具类
 * 
 * 提供旧接口到新接口的无缝转换
 * 确保向后兼容性和数据完整性
 * 
 * <AUTHOR> Sonnet 4 深度分析
 * @version 2.0.0
 */

import {
  GlobalServiceInstance,
  ServerAwareServiceInstance,
  UniversalServiceInstance,
  createGlobalServiceInstance,
  createServerAwareServiceInstance,
  isGlobalServiceInstance,
  isServerAwareServiceInstance,
} from '../interfaces/service-instance.interfaces';

/**
 * 实例迁移工具类
 * 
 * 基于Adapter设计模式，提供旧接口到新接口的适配
 */
export class InstanceMigrationUtils {
  
  /**
   * 🔄 迁移旧的StoredGlobalServiceInstance到新的GlobalServiceInstance
   */
  static migrateStoredGlobalInstance(oldInstance: any): GlobalServiceInstance {
    // 处理时间字段的类型转换
    const registeredAt = this.parseDate(oldInstance.registeredAt);
    const lastHeartbeat = this.parseDate(oldInstance.lastHeartbeat);
    
    return createGlobalServiceInstance(
      {
        serviceName: oldInstance.serviceName,
        host: oldInstance.host,
        port: oldInstance.port,
        weight: oldInstance.weight,
        metadata: oldInstance.metadata,
      },
      {
        id: oldInstance.instanceId || oldInstance.id,
        registeredAt,
        lastHeartbeat,
        healthy: oldInstance.healthy ?? true,
      }
    );
  }

  /**
   * 🔄 迁移旧的ServerAwareServiceInstance到新格式
   */
  static migrateServerAwareInstance(oldInstance: any): ServerAwareServiceInstance {
    // 处理时间字段的类型转换
    const registeredAt = this.parseDate(oldInstance.registeredAt);
    const lastHeartbeat = this.parseDate(oldInstance.lastHeartbeat);
    const lastHealthCheck = this.parseDate(oldInstance.lastHealthCheck);
    
    return createServerAwareServiceInstance(
      {
        serviceName: oldInstance.serviceName,
        serverId: oldInstance.serverId,
        instanceName: oldInstance.instanceName,
        host: oldInstance.host,
        port: oldInstance.port,
        weight: oldInstance.weight,
        metadata: oldInstance.metadata,
      },
      {
        id: oldInstance.id,
        registeredAt,
        lastHeartbeat,
        lastHealthCheck,
        healthy: oldInstance.healthy ?? true,
        connections: oldInstance.connections ?? 0,
        responseTime: oldInstance.responseTime ?? 0,
      }
    );
  }

  /**
   * 🔄 迁移通用ServiceInstance到新格式
   */
  static migrateServiceInstance(oldInstance: any): UniversalServiceInstance {
    // 根据字段判断服务类型
    if (oldInstance.serverId && oldInstance.instanceName) {
      // 区服感知服务
      return this.migrateServerAwareInstance(oldInstance);
    } else {
      // 全局服务
      return this.migrateStoredGlobalInstance(oldInstance);
    }
  }

  /**
   * 📦 转换新的GlobalServiceInstance到存储格式
   */
  static toStorageFormat(instance: GlobalServiceInstance): any {
    return {
      instanceId: instance.id,
      serviceName: instance.serviceName,
      serviceType: instance.serviceType,
      host: instance.host,
      port: instance.port,
      healthy: instance.healthy,
      weight: instance.weight,
      registeredAt: instance.registeredAt.toISOString(),
      lastHeartbeat: instance.lastHeartbeat.toISOString(),
      metadata: instance.metadata,
    };
  }

  /**
   * 📦 转换新的ServerAwareServiceInstance到存储格式
   */
  static toServerAwareStorageFormat(instance: ServerAwareServiceInstance): any {
    return {
      id: instance.id,
      serviceName: instance.serviceName,
      serviceType: instance.serviceType,
      serverId: instance.serverId,
      instanceName: instance.instanceName,
      host: instance.host,
      port: instance.port,
      healthy: instance.healthy,
      weight: instance.weight,
      connections: instance.connections,
      responseTime: instance.responseTime,
      registeredAt: instance.registeredAt.toISOString(),
      lastHeartbeat: instance.lastHeartbeat.toISOString(),
      lastHealthCheck: instance.lastHealthCheck.toISOString(),
      metadata: instance.metadata,
    };
  }

  /**
   * 📦 通用存储格式转换
   */
  static toUniversalStorageFormat(instance: UniversalServiceInstance): any {
    if (isGlobalServiceInstance(instance)) {
      return this.toStorageFormat(instance);
    } else if (isServerAwareServiceInstance(instance)) {
      return this.toServerAwareStorageFormat(instance);
    } else {
      throw new Error(`Unknown service instance type: ${(instance as any).serviceType}`);
    }
  }

  /**
   * 🔄 批量迁移实例数组
   */
  static migrateBatch(oldInstances: any[]): UniversalServiceInstance[] {
    return oldInstances.map(instance => this.migrateServiceInstance(instance));
  }

  /**
   * 🔍 检测实例类型
   */
  static detectInstanceType(instance: any): 'global' | 'server-aware' | 'unknown' {
    if (instance.serviceType) {
      return instance.serviceType;
    }
    
    // 基于字段推断类型
    if (instance.serverId && instance.instanceName) {
      return 'server-aware';
    } else if (instance.serviceName && instance.host && instance.port) {
      return 'global';
    } else {
      return 'unknown';
    }
  }

  /**
   * 📊 生成迁移报告
   */
  static generateMigrationReport(oldInstances: any[]): {
    total: number;
    global: number;
    serverAware: number;
    unknown: number;
    errors: string[];
  } {
    const report = {
      total: oldInstances.length,
      global: 0,
      serverAware: 0,
      unknown: 0,
      errors: [] as string[],
    };

    oldInstances.forEach((instance, index) => {
      try {
        const type = this.detectInstanceType(instance);
        switch (type) {
          case 'global':
            report.global++;
            break;
          case 'server-aware':
            report.serverAware++;
            break;
          default:
            report.unknown++;
            report.errors.push(`Instance ${index}: Unknown type`);
        }
      } catch (error) {
        report.errors.push(`Instance ${index}: ${error.message}`);
      }
    });

    return report;
  }

  /**
   * 🛠️ 修复损坏的实例数据
   */
  static repairInstance(instance: any): any {
    const repaired = { ...instance };

    // 修复缺失的ID
    if (!repaired.id && !repaired.instanceId) {
      repaired.id = this.generateFallbackId(repaired);
    }

    // 统一ID字段
    if (repaired.instanceId && !repaired.id) {
      repaired.id = repaired.instanceId;
    }

    // 修复权重
    if (typeof repaired.weight !== 'number' || repaired.weight < 0) {
      repaired.weight = 1;
    }

    // 修复健康状态
    if (typeof repaired.healthy !== 'boolean') {
      repaired.healthy = true;
    }

    // 修复元数据
    if (!repaired.metadata || typeof repaired.metadata !== 'object') {
      repaired.metadata = {};
    }

    // 修复时间字段
    const now = new Date();
    if (!repaired.registeredAt) {
      repaired.registeredAt = now.toISOString();
    }
    if (!repaired.lastHeartbeat) {
      repaired.lastHeartbeat = now.toISOString();
    }

    return repaired;
  }

  /**
   * 🕒 解析时间字段
   */
  private static parseDate(dateValue: any): Date {
    if (!dateValue) {
      return new Date();
    }
    
    if (dateValue instanceof Date) {
      return dateValue;
    }
    
    if (typeof dateValue === 'string') {
      const parsed = new Date(dateValue);
      return isNaN(parsed.getTime()) ? new Date() : parsed;
    }
    
    if (typeof dateValue === 'number') {
      return new Date(dateValue);
    }
    
    return new Date();
  }

  /**
   * 🆔 生成备用ID - 使用新的差异化策略
   */
  private static generateFallbackId(instance: any): string {
    const serviceName = instance.serviceName || 'unknown';
    const host = (instance.host || 'localhost').replace(/\./g, '-');
    const port = instance.port || 0;

    // 判断是否为区服服务
    if (instance.serverId && instance.instanceName) {
      // 区服服务：instanceName-instanceId-host-port
      const instanceId = instance.instanceId || 0;
      return `${instance.instanceName}-${instanceId}-${host}-${port}`;
    } else {
      // 全局服务：serviceName-host-port
      return `${serviceName}-${host}-${port}`;
    }
  }

  /**
   * 🔄 创建向后兼容的适配器
   */
  static createLegacyAdapter(newInstance: UniversalServiceInstance): any {
    if (isGlobalServiceInstance(newInstance)) {
      return {
        instanceId: newInstance.id,
        serviceName: newInstance.serviceName,
        host: newInstance.host,
        port: newInstance.port,
        healthy: newInstance.healthy,
        weight: newInstance.weight,
        registeredAt: newInstance.registeredAt.toISOString(),
        lastHeartbeat: newInstance.lastHeartbeat.toISOString(),
        metadata: newInstance.metadata,
      };
    } else if (isServerAwareServiceInstance(newInstance)) {
      return {
        id: newInstance.id,
        serviceName: newInstance.serviceName,
        serverId: newInstance.serverId,
        instanceName: newInstance.instanceName,
        host: newInstance.host,
        port: newInstance.port,
        healthy: newInstance.healthy,
        weight: newInstance.weight,
        connections: newInstance.connections,
        responseTime: newInstance.responseTime,
        registeredAt: newInstance.registeredAt,
        lastHeartbeat: newInstance.lastHeartbeat,
        lastHealthCheck: newInstance.lastHealthCheck,
        metadata: newInstance.metadata,
      };
    }
    
    throw new Error(`Cannot create legacy adapter for unknown instance type`);
  }
}
