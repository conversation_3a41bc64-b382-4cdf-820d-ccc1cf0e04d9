/**
 * 服务实例迁移工具类
 * 
 * 提供旧接口到新接口的无缝转换
 * 确保向后兼容性和数据完整性
 * 
 * <AUTHOR> Sonnet 4 深度分析
 * @version 2.0.0
 */

import {
  GlobalServiceInstance,
  ServerAwareServiceInstance,
  UniversalServiceInstance,
  createGlobalServiceInstance,
  createServerAwareServiceInstance,
  isGlobalServiceInstance,
  isServerAwareServiceInstance,
} from '../interfaces/service-instance.interfaces';

/**
 * 实例迁移工具类
 * 
 * 基于Adapter设计模式，提供旧接口到新接口的适配
 */
export class InstanceMigrationUtils {
  
  /**
   * 🔄 迁移旧的StoredGlobalServiceInstance到新的GlobalServiceInstance
   */
  static migrateStoredGlobalInstance(oldInstance: any): GlobalServiceInstance {
    // 处理时间字段的类型转换
    const registeredAt = this.parseDate(oldInstance.registeredAt);
    const lastHeartbeat = this.parseDate(oldInstance.lastHeartbeat);
    
    return createGlobalServiceInstance(
      {
        serviceName: oldInstance.serviceName,
        host: oldInstance.host,
        port: oldInstance.port,
        weight: oldInstance.weight,
        metadata: oldInstance.metadata,
      },
      {
        id: oldInstance.instanceId || oldInstance.id,
        registeredAt,
        lastHeartbeat,
        healthy: oldInstance.healthy ?? true,
      }
    );
  }

  /**
   * 📦 转换新的GlobalServiceInstance到存储格式
   */
  static toStorageFormat(instance: GlobalServiceInstance): any {
    return {
      instanceId: instance.id,
      serviceName: instance.serviceName,
      serviceType: instance.serviceType,
      host: instance.host,
      port: instance.port,
      healthy: instance.healthy,
      weight: instance.weight,
      registeredAt: instance.registeredAt.toISOString(),
      lastHeartbeat: instance.lastHeartbeat.toISOString(),
      metadata: instance.metadata,
    };
  }

  /**
   * 🕒 解析时间字段
   */
  private static parseDate(dateValue: any): Date {
    if (!dateValue) {
      return new Date();
    }
    
    if (dateValue instanceof Date) {
      return dateValue;
    }
    
    if (typeof dateValue === 'string') {
      const parsed = new Date(dateValue);
      return isNaN(parsed.getTime()) ? new Date() : parsed;
    }
    
    if (typeof dateValue === 'number') {
      return new Date(dateValue);
    }
    
    return new Date();
  }

}
