import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServerAwareRegistryService } from './server-aware-registry.service';
import {ServerAwareRegistrationRequest, ServiceConfiguration} from '../interfaces/service-instance.interfaces';

/**
 * 🎯 优化的服务自动注册服务
 *
 * 核心特性：
 * - 支持预计算配置：避免重复计算
 * - 零侵入性：业务代码无需修改
 * - 生命周期管理：自动注册和注销
 * - 错误容错：注册失败不影响服务启动
 * - 架构统一：与ServiceConfigurationBuilder完美集成
 */
@Injectable()
export class ServiceAutoRegistrationService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceAutoRegistrationService.name);
  private instanceId: string | null = null;

  constructor(
    @Inject('SERVICE_REGISTRATION_CONFIG') private readonly config: {
      serviceName: string;
      autoRegister: boolean;
      weight: number;
      metadata: Record<string, any>;
      preCalculatedConfig: ServiceConfiguration; // 🎯 必须提供预计算配置
    },
    private readonly configService: ConfigService,
    private readonly serverAwareRegistry: ServerAwareRegistryService,
  ) {}

  async onModuleInit() {
    if (this.config.autoRegister) {
      await this.registerService();
    }
  }

  async onModuleDestroy() {
    if (this.instanceId) {
      await this.unregisterService();
    }
  }

  /**
   * 🎯 注册服务到区服感知服务注册中心（支持预计算配置）
   */
  private async registerService(): Promise<void> {
    try {
      const preCalculatedConfig = this.config.preCalculatedConfig;
      // 🎯 直接使用预计算配置（必须存在）
      if (!preCalculatedConfig) {
        throw new Error('❌ 预计算配置不存在，无法自动注册');
      }

      // 注册请求
      const registerRequest: ServerAwareRegistrationRequest = {
        serviceName: preCalculatedConfig.serviceName,
        serverId: preCalculatedConfig.serverId,
        host: preCalculatedConfig.host,
        port: preCalculatedConfig.port,
        instanceIndex: preCalculatedConfig.instanceIndex,
        healthy: true,
        weight: this.config.weight,
        metadata: {
          ...preCalculatedConfig.metadata,
          // 合并用户自定义元数据
          ...this.config.metadata,
          // 确保关键字段不被覆盖
          registrationMode: 'ServiceMeshModule.register.preCalculated',
          configurationSource: 'ServiceConfigurationBuilder',
          autoRegistered: true,
        },
      }

      this.instanceId = await this.serverAwareRegistry.registerInstance(registerRequest);

      this.logger.log(`✅ 预计算配置自动注册成功!`);
      this.logger.log(`   🆔 实例ID: ${this.instanceId}`);
      this.logger.log(`   📍 注册地址: service_registry:instances:${preCalculatedConfig.serviceName}:${preCalculatedConfig.serverId}`);
      this.logger.log(`   🔗 健康检查: http://${preCalculatedConfig.host}:${preCalculatedConfig.port}/health`);
      this.logger.log(`🎉 ${preCalculatedConfig.serviceName} 服务已成功接入区服感知架构!`);
    } catch (error) {
      this.logger.error(`❌ 自动注册失败: ${error.message}`, error.stack);
      // 注册失败不应该阻止服务启动
    }
  }

  /**
   * 🎯 从服务注册中心注销服务（完整版）
   */
  private async unregisterService(): Promise<void> {
    if (!this.instanceId) {
      this.logger.debug(`⚠️ 跳过注销：未找到实例ID`);
      return;
    }

    if (!this.config.preCalculatedConfig) {
      this.logger.warn(`⚠️ 跳过注销：缺少预计算配置`);
      return;
    }

    try {
      const { serviceName, serverId } = this.config.preCalculatedConfig;

      this.logger.log(`🔄 开始注销服务实例:`);
      this.logger.log(`   📋 服务名称: ${serviceName}`);
      this.logger.log(`   🏰 区服ID: ${serverId}`);
      this.logger.log(`   🆔 实例ID: ${this.instanceId}`);

      // 🎯 执行实际的注销操作
      // 从instanceId提取instanceName
      const instanceName = this.extractInstanceNameFromId(this.instanceId);

      const success = await this.serverAwareRegistry.unregisterInstance(
        serviceName,
        serverId,
        instanceName
      );

      if (success) {
        this.logger.log(`✅ 服务实例注销成功: ${this.instanceId}`);
        this.logger.log(`   📍 注销地址: service_registry:instances:${serviceName}:${serverId}`);

        // 清理实例ID
        this.instanceId = null;
      } else {
        this.logger.warn(`⚠️ 服务实例注销失败: 实例不存在或已被移除`);
        this.logger.warn(`   🆔 实例ID: ${this.instanceId}`);
      }

    } catch (error) {
      this.logger.error(`❌ 自动注销失败: ${error.message}`, error.stack);
      this.logger.error(`   🆔 实例ID: ${this.instanceId}`);
      this.logger.error(`   📋 服务名称: ${this.config.preCalculatedConfig?.serviceName || 'unknown'}`);
    }
  }

  /**
   * 🎯 获取当前服务注册状态
   */
  getRegistrationStatus(): {
    registered: boolean;
    serviceName: string;
    instanceId: string | null;
    autoRegister: boolean;
  } {
    return {
      registered: !!this.instanceId,
      serviceName: this.config.serviceName,
      instanceId: this.instanceId || null,
      autoRegister: this.config.autoRegister,
    };
  }
}
