import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PortManager } from '@port-manager';
import { ServerAwareRegistryService } from './server-aware-registry.service';

/**
 * 服务自动注册服务
 * 
 * 借鉴 microservice-kit 的设计模式，提供零侵入性的服务注册功能。
 * 通过 NestJS 生命周期钩子自动处理服务注册和注销。
 * 
 * 核心特性：
 * - 零侵入性：业务代码无需修改
 * - 配置驱动：所有参数从环境变量获取
 * - 生命周期管理：自动注册和注销
 * - 错误容错：注册失败不影响服务启动
 */
@Injectable()
export class ServiceAutoRegistrationService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceAutoRegistrationService.name);
  private instanceId: string | null = null;
  private serverId: string;
  private instanceName: string;

  constructor(
    @Inject('SERVICE_REGISTRATION_CONFIG') private readonly config: {
      serviceName: string;
      serverId?: string; // 🎯 新增：显式配置的serverId
      autoRegister: boolean;
      weight: number;
      metadata: Record<string, any>;
    },
    private readonly configService: ConfigService,
    private readonly serverAwareRegistry: ServerAwareRegistryService,
  ) {}

  async onModuleInit() {
    if (this.config.autoRegister) {
      await this.registerService();
    }
  }

  async onModuleDestroy() {
    if (this.instanceId) {
      await this.unregisterService();
    }
  }

  /**
   * 注册服务到区服感知服务注册中心
   */
  private async registerService(): Promise<void> {
    try {
      // 获取配置参数
      this.serverId = this.getServerId();
      this.instanceName = this.getInstanceName();
      const port = this.getServicePort();
      const environment = this.configService.get<string>('NODE_ENV', 'development');

      this.logger.log(`🏷️ 自动注册配置详情:`);
      this.logger.log(`   📋 服务名称: ${this.config.serviceName}`);
      this.logger.log(`   🏰 区服ID: ${this.serverId}`);
      this.logger.log(`   🏷️ 实例名称: ${this.instanceName}`);
      this.logger.log(`   🌐 监听地址: 127.0.0.1:${port}`);
      this.logger.log(`   ⚖️ 负载权重: ${this.config.weight}`);
      this.logger.log(`   🌍 运行环境: ${environment}`);
      this.logger.log(`   🔧 自动注册: ${this.config.autoRegister ? '启用' : '禁用'}`);

      // ✅ 执行注册（支持动态端口）
      this.instanceId = await this.serverAwareRegistry.registerInstance({
        serviceName: this.config.serviceName,
        serverId: this.serverId,
        host: '127.0.0.1',
        port: port, // 可能是动态计算的端口
        instanceIndex: parseInt(this.configService.get('INSTANCE_INDEX') || '0'), // ✅ 添加实例ID
        healthy: true,
        weight: this.config.weight,
        metadata: {
          version: '1.0.0',
          environment: environment,
          startTime: new Date().toISOString(),
          autoRegistered: true,
          registrationMode: 'ServiceMeshModule.register',
          serverId: this.serverId.trim(), // ✅ 确保元数据中的区服ID没有空格
          instanceId: parseInt(this.configService.get('INSTANCE_INDEX') || '0'),
          // ✅ 合并用户自定义元数据，但确保port使用实际的动态端口
          ...this.config.metadata,
          port: port, // ✅ 强制使用实际的动态端口，覆盖配置中的固定端口
        },
      });

      this.logger.log(`✅ 自动注册成功!`);
      this.logger.log(`   🆔 实例ID: ${this.instanceId}`);
      this.logger.log(`   📍 注册地址: service_registry:instances:${this.config.serviceName}:${this.serverId}`);
      this.logger.log(`   🔗 健康检查: http://127.0.0.1:${port}/health`);
      this.logger.log(`   📊 元数据: ${JSON.stringify(this.config.metadata, null, 2).replace(/\n/g, '\n      ')}`);
      this.logger.log(`🎉 ${this.config.serviceName} 服务已成功接入区服感知架构!`);
    } catch (error) {
      this.logger.error(`❌ 自动注册失败: ${error.message}`, error.stack);
      // 注册失败不应该阻止服务启动
    }
  }

  /**
   * 从服务注册中心注销服务
   */
  private async unregisterService(): Promise<void> {
    if (!this.instanceId || !this.serverId || !this.instanceName) {
      return;
    }

    try {
      const success = this.serverAwareRegistry.unregisterInstance(
        this.config.serviceName,
        this.serverId,
        this.instanceName
      );

      if (success) {
        this.logger.log(`✅ 自动注销成功: ${this.instanceName}`);
      } else {
        this.logger.warn(`⚠️ 自动注销失败: 实例不存在或已被移除`);
      }
    } catch (error) {
      this.logger.error(`❌ 自动注销失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取区服ID
   * 🎯 优化：优先使用显式配置，然后回退到环境变量
   * 支持多种配置键名，优先级从高到低
   */
  private getServerId(): string {
    // 🎯 最高优先级：显式配置的serverId（与RedisModule保持一致）
    if (this.config.serverId) {
      const trimmedServerId = this.config.serverId.trim(); // ✅ 去除空格
      this.logger.debug(`🎯 使用显式配置的区服ID: ${trimmedServerId}`);
      return trimmedServerId;
    }

    // 🔄 回退机制：从环境变量获取（与RedisModule保持一致的优先级）
    const serverId = (this.configService.get('SERVER_ID') ||
                     this.configService.get('DEFAULT_SERVER_ID') ||
                     this.configService.get('CURRENT_SERVER_ID') ||
                     this.configService.get(`${this.config.serviceName.toUpperCase()}_SERVER_ID`) ||
                     'server_001')?.trim(); // ✅ 去除空格

    // 验证区服ID格式
    if (!/^server_\d{3}$/.test(serverId)) {
      this.logger.warn(`⚠️ 区服ID格式不标准: ${serverId}，建议使用 server_xxx 格式`);
    }

    this.logger.debug(`🔄 使用环境变量区服ID: ${serverId}`);
    return serverId;
  }

  /**
   * 获取实例名称
   */
  private getInstanceName(): string {
    const customInstanceId = (this.configService.get('INSTANCE_INDEX') ||
                             this.configService.get(`${this.config.serviceName.toUpperCase()}_INSTANCE_ID`))?.trim(); // ✅ 去除空格

    if (customInstanceId) {
      return customInstanceId;
    }

    // 自动生成实例名称
    const instanceNumber = this.configService.get<number>('INSTANCE_NUMBER') ||
                           this.configService.get<number>(`${this.config.serviceName.toUpperCase()}_INSTANCE_NUMBER`) ||
                           1;
    
    return `${this.config.serviceName}-${this.serverId}-${instanceNumber}`;
  }

  /**
   * 获取服务端口
   * 支持动态端口计算和固定端口配置
   */
  private getServicePort(): number {
    const serviceName = this.config.serviceName;
    const serverId = this.serverId;
    const instanceId = parseInt((this.configService.get('INSTANCE_INDEX') || '0').trim()); // ✅ 去除空格

    try {
      // ✅ 优先使用动态端口计算
      const dynamicPort = PortManager.calculatePort(serviceName, serverId, instanceId);
      this.logger.log(`🔢 动态计算端口: ${serviceName}@${serverId} 实例${instanceId} -> ${dynamicPort}`);
      return dynamicPort;
    } catch (error) {
      // ✅ 降级到传统端口配置
      this.logger.warn(`⚠️ 动态端口计算失败，使用固定端口配置: ${error.message}`);

      const serviceNameUpper = serviceName.toUpperCase();
      const port = this.configService.get<number>(`${serviceNameUpper}_BASE_PORT`) ||
        this.configService.get<number>(`${serviceNameUpper}_PORT`) ||
        this.configService.get<number>('PORT');

      this.logger.log(`🔧 使用固定端口: ${serviceName} -> ${port}`);
      return port;
    }
  }

  /**
   * 获取当前服务注册状态
   */
  getRegistrationStatus(): {
    registered: boolean;
    serviceName: string;
    serverId: string | null;
    instanceName: string | null;
    instanceId: string | null;
    autoRegister: boolean;
  } {
    return {
      registered: !!this.instanceId,
      serviceName: this.config.serviceName,
      serverId: this.serverId || null,
      instanceName: this.instanceName || null,
      instanceId: this.instanceId || null,
      autoRegister: this.config.autoRegister,
    };
  }
}
