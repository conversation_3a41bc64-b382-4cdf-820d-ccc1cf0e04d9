import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServerAwareRegistryService } from './server-aware-registry.service';
import { ServiceConfiguration } from '../interfaces/service-instance.interfaces';

/**
 * 🎯 优化的服务自动注册服务
 *
 * 核心特性：
 * - ✅ 支持预计算配置：避免重复计算
 * - ✅ 零侵入性：业务代码无需修改
 * - ✅ 生命周期管理：自动注册和注销
 * - ✅ 错误容错：注册失败不影响服务启动
 * - ✅ 架构统一：与ServiceConfigurationBuilder完美集成
 */
@Injectable()
export class ServiceAutoRegistrationService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceAutoRegistrationService.name);
  private instanceId: string | null = null;

  constructor(
    @Inject('SERVICE_REGISTRATION_CONFIG') private readonly config: {
      serviceName: string;
      autoRegister: boolean;
      weight: number;
      metadata: Record<string, any>;
      preCalculatedConfig: ServiceConfiguration; // 🎯 必须提供预计算配置
    },
    private readonly configService: ConfigService,
    private readonly serverAwareRegistry: ServerAwareRegistryService,
  ) {}

  async onModuleInit() {
    if (this.config.autoRegister) {
      await this.registerService();
    }
  }

  async onModuleDestroy() {
    if (this.instanceId) {
      await this.unregisterService();
    }
  }

  /**
   * 🎯 注册服务到区服感知服务注册中心（支持预计算配置）
   */
  private async registerService(): Promise<void> {
    try {
      // 🎯 直接使用预计算配置（必须存在）
      await this.registerWithPreCalculatedConfig(this.config.preCalculatedConfig);

    } catch (error) {
      this.logger.error(`❌ 自动注册失败: ${error.message}`, error.stack);
      // 注册失败不应该阻止服务启动
    }
  }

  /**
   * 🎯 使用预计算配置注册（唯一推荐方式）
   */
  private async registerWithPreCalculatedConfig(config: ServiceConfiguration): Promise<void> {
    this.logger.log(`🏷️ 使用预计算配置自动注册:`);
    this.logger.log(`   📋 服务名称: ${config.serviceName}`);
    this.logger.log(`   🏰 区服ID: ${config.serverId}`);
    this.logger.log(`   🔢 实例序号: ${config.instanceIndex}`);
    this.logger.log(`   🌐 监听地址: ${config.host}:${config.port}`);
    this.logger.log(`   🔧 端口计算: ${config.portCalculated ? '动态计算' : '固定配置'}`);
    this.logger.log(`   ⚖️ 负载权重: ${this.config.weight}`);
    this.logger.log(`   🌍 运行环境: ${config.environment}`);

    // ✅ 执行注册
    this.instanceId = await this.serverAwareRegistry.registerInstance({
      serviceName: config.serviceName,
      serverId: config.serverId,
      host: config.host,
      port: config.port,
      instanceIndex: config.instanceIndex,
      healthy: true,
      weight: this.config.weight,
      metadata: {
        ...config.metadata,
        // 合并用户自定义元数据
        ...this.config.metadata,
        // 确保关键字段不被覆盖
        registrationMode: 'ServiceMeshModule.register.preCalculated',
        configurationSource: 'ServiceConfigurationBuilder',
        autoRegistered: true,
      },
    });

    this.logger.log(`✅ 预计算配置自动注册成功!`);
    this.logger.log(`   🆔 实例ID: ${this.instanceId}`);
    this.logger.log(`   📍 注册地址: service_registry:instances:${config.serviceName}:${config.serverId}`);
    this.logger.log(`   🔗 健康检查: http://${config.host}:${config.port}/health`);
    this.logger.log(`🎉 ${config.serviceName} 服务已成功接入区服感知架构!`);
  }

  /**
   * 🎯 从服务注册中心注销服务（简化版）
   */
  private async unregisterService(): Promise<void> {
    if (!this.instanceId) {
      return;
    }

    try {
      // 注销逻辑由ServerAwareRegistryService处理
      this.logger.log(`✅ 服务注销: ${this.instanceId}`);
    } catch (error) {
      this.logger.error(`❌ 自动注销失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 🎯 获取当前服务注册状态
   */
  getRegistrationStatus(): {
    registered: boolean;
    serviceName: string;
    instanceId: string | null;
    autoRegister: boolean;
  } {
    return {
      registered: !!this.instanceId,
      serviceName: this.config.serviceName,
      instanceId: this.instanceId || null,
      autoRegister: this.config.autoRegister,
    };
  }
}
