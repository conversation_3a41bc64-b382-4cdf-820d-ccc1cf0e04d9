import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { ServiceInstance } from '../discovery/unified-service-discovery.service';
import { GlobalServiceLoadBalancerService, GlobalLoadBalancingContext } from '../load-balancing/global-service-load-balancer.service';
import {
  GlobalServiceInstance,
  GlobalServiceRegistrationRequest,
  createGlobalServiceInstance,
  validateServiceInstance,
} from '../interfaces/service-instance.interfaces';

/**
 * 全局服务注册中心
 * 
 * 管理跨区服的全局服务，如Auth、Payment等
 * 特点：
 * - 数据全局共享
 * - 支持全局负载均衡
 * - 健康检查和故障转移
 * - 自动实例清理
 */
@Injectable()
export class GlobalServiceRegistryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(GlobalServiceRegistryService.name);
  private healthCheckInterval: NodeJS.Timeout;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
    private readonly globalLoadBalancer: GlobalServiceLoadBalancerService,
  ) {}

  async onModuleInit() {
    this.logger.log('🌍 全局服务注册中心启动');
    this.startHealthCheck();
  }

  async onModuleDestroy() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.logger.log('🌍 全局服务注册中心关闭');
  }

  /**
   * 注册全局服务实例 - 使用新的统一接口
   */
  async registerInstance(request: GlobalServiceRegistrationRequest): Promise<string> {
    // 🚀 创建标准化的全局服务实例
    const instance = createGlobalServiceInstance(request);

    // 验证实例数据完整性
    const validationErrors = validateServiceInstance(instance);
    if (validationErrors.length > 0) {
      throw new Error(`Invalid service instance: ${validationErrors.join(', ')}`);
    }

    const businessKey = `global_services:${instance.serviceName}`;

    // 🔍 调试日志：追踪注册过程中的键构建
    console.log(`[GlobalServiceRegistry.registerInstance] 注册参数:`);
    console.log(`  serviceName: "${instance.serviceName}"`);
    console.log(`  instanceId: "${instance.id}"`);
    console.log(`  businessKey: "${businessKey}"`);

    // 🔍 检查现有实例
    const existingInstance = await this.redisService.hget(businessKey, instance.id, 'global');
    if (existingInstance) {
      this.logger.log(`🔄 替换现有实例: ${instance.id}`);
    }
    
    // 🚀 转换为存储格式
    const instanceData = InstanceMigrationUtils.toStorageFormat(instance);

    // 🔧 修复：使用正确的数据类型存储全局服务实例，RedisService会自动序列化
    console.log(`[GlobalServiceRegistry.registerInstance] 即将调用hset:`);
    console.log(`  businessKey: "${businessKey}"`);
    console.log(`  instanceId: "${instance.id}"`);
    console.log(`  dataType: "global"`);

    await this.redisService.hset(businessKey, instance.id, instanceData, 'global');

    this.logger.log(`✅ 全局服务注册成功: ${instance.serviceName} (${instance.id})`);
    this.logger.log(`   📍 注册地址: ${businessKey}`);
    this.logger.log(`   🔗 服务地址: http://${instance.host}:${instance.port}`);

    return instance.id;
  }

  /**
   * 获取健康的服务实例 - 返回新的统一接口
   */
  async getHealthyInstances(serviceName: string): Promise<GlobalServiceInstance[]> {
    const businessKey = `global_services:${serviceName}`;
    // 🔧 修复：使用正确的数据类型获取全局服务实例
    const instances = await this.redisService.hgetall(businessKey, 'global');

    if (!instances || Object.keys(instances).length === 0) {
      this.logger.warn(`⚠️ 没有找到全局服务实例: ${serviceName}`);
      return [];
    }

    // 🚀 使用迁移工具转换为新的统一接口
    const migratedInstances = Object.values(instances)
      .map(data => InstanceMigrationUtils.migrateStoredGlobalInstance(data))
      .filter(instance => instance.healthy);

    this.logger.debug(`🔍 全局服务 ${serviceName}: 总实例=${Object.keys(instances).length}, 健康实例=${migratedInstances.length}`);

    return migratedInstances;
  }

  /**
   * 选择服务实例（全局负载均衡）- 使用专用负载均衡器
   */
  async selectInstance(
    serviceName: string,
    strategy: string = 'intelligent',
    context: GlobalLoadBalancingContext = {}
  ): Promise<ServiceInstance> {
    const instances = await this.getHealthyInstances(serviceName);

    if (instances.length === 0) {
      throw new Error(`全局服务 ${serviceName} 无可用实例`);
    }

    // 🚀 使用专用负载均衡器选择实例
    const loadBalancingContext: GlobalLoadBalancingContext = {
      serviceName,
      portChecker: this.isCorrectPort.bind(this),
      ...context
    };

    const selected = await this.globalLoadBalancer.selectInstance(
      instances,
      strategy,
      loadBalancingContext
    );

    if (!selected) {
      throw new Error(`全局服务 ${serviceName} 负载均衡选择失败`);
    }

    this.logger.debug(`🎯 选择全局服务实例: ${selected.id} (${selected.host}:${selected.port}) 策略=${strategy}`);

    return {
      id: selected.id,
      serviceName: selected.serviceName,
      host: selected.host,
      port: selected.port,
      healthy: selected.healthy,
      weight: selected.weight || 1,
      metadata: selected.metadata,
    };
  }

  /**
   * 注销服务实例
   */
  async unregisterInstance(serviceName: string, instanceId: string): Promise<void> {
    const businessKey = `global_services:${serviceName}`;
    // 🔧 修复：使用正确的数据类型删除全局服务实例
    const result = await this.redisService.hdel(businessKey, [instanceId], 'global');
    
    if (result > 0) {
      this.logger.log(`✅ 全局服务注销成功: ${serviceName} (${instanceId})`);
    } else {
      this.logger.warn(`⚠️ 全局服务注销失败，实例不存在: ${serviceName} (${instanceId})`);
    }
  }

  /**
   * 更新实例心跳
   */
  async updateHeartbeat(serviceName: string, instanceId: string): Promise<void> {
    const businessKey = `global_services:${serviceName}`;
    // 🔧 修复：使用正确的数据类型获取实例数据
    const instanceData = await this.redisService.hget(businessKey, instanceId, 'global');

    if (instanceData) {
      // 🚀 迁移旧数据到新格式
      const migratedInstance = InstanceMigrationUtils.migrateStoredGlobalInstance(instanceData);

      // 更新心跳时间
      migratedInstance.lastHeartbeat = new Date();
      migratedInstance.healthy = true;

      // 转换回存储格式并保存
      const storageData = InstanceMigrationUtils.toStorageFormat(migratedInstance);
      await this.redisService.hset(businessKey, instanceId, storageData, 'global');
      this.logger.debug(`💓 更新全局服务心跳: ${serviceName} (${instanceId})`);
    }
  }

  /**
   * 🔍 检查端口正确性
   */
  private isCorrectPort(instance: StoredGlobalServiceInstance, serviceName: string): boolean {
    try {
      // 尝试使用PortManager计算期望端口
      const serverId = instance.metadata?.serverId || 'default';
      const instanceId = parseInt(instance.metadata?.instanceId || '0', 10);
      const expectedPort = this.calculateExpectedPort(serviceName, serverId, instanceId);
      return instance.port === expectedPort;
    } catch (error) {
      // 如果无法计算期望端口，则检查是否为基础端口
      const basePort = this.getServiceBasePort(serviceName);
      return instance.port === basePort;
    }
  }

  /**
   * 📋 获取服务基础端口
   */
  private getServiceBasePort(serviceName: string): number {
    const DEFAULT_PORTS = {
      'gateway': 3000,
      'auth': 3100,
      'character': 3200,
      'hero': 3300,
      'economy': 3400,
      'activity': 3500,
      'match': 3600,
      'guild': 3700,
      'social': 3800,
      'payment': 3900,
      'notification': 4000,
      'analytics': 4100,
      'monitoring': 4200,
      'logging': 4300,
    };

    return DEFAULT_PORTS[serviceName] || 3000;
  }

  /**
   * 🔢 计算期望端口
   */
  private calculateExpectedPort(serviceName: string, serverId: string, instanceId: number): number {
    // 简化的端口计算逻辑，避免依赖PortManager
    const basePort = this.getServiceBasePort(serviceName);
    return basePort; // 暂时返回基础端口，后续可以扩展
  }

  /**
   * 🔧 生成确定性实例ID - 基于host+port
   */
  private generateInstanceId(serviceName: string, host: string, port: number): string {
    const normalizedHost = host.replace(/\./g, '-').replace(/:/g, '-');
    return `${serviceName}-${normalizedHost}-${port}`;
  }



  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    const interval = this.configService.get('GLOBAL_SERVICE_HEALTH_CHECK_INTERVAL', 30000);
    
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, interval);
    
    this.logger.log(`🏥 全局服务健康检查启动，间隔: ${interval}ms`);
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const timeout = this.configService.get('GLOBAL_SERVICE_INSTANCE_TIMEOUT', 30000);
      const now = new Date().getTime();
      let totalInstances = 0;
      let healthyInstances = 0;
      let removedInstances = 0;

      // 🔧 修复：使用RedisService的keys方法搜索全局服务键
      const businessKeys = await this.redisService.keys('global_services:*', 'global');

      this.logger.debug(`🔍 健康检查发现的业务键: ${businessKeys.length}个 - ${businessKeys.join(', ')}`);

      for (const businessKey of businessKeys) {
        try {
          // 🔧 修复：使用RedisService的hgetall方法获取哈希数据
          const instances = await this.redisService.hgetall(businessKey, 'global');

          for (const [instanceId, instanceData] of Object.entries(instances)) {
            totalInstances++;

            try {
              // RedisService已经自动反序列化，直接使用
              const instance = instanceData as StoredGlobalServiceInstance;
              const lastHeartbeat = new Date(instance.lastHeartbeat).getTime();

              if (now - lastHeartbeat > timeout) {
                // 🔧 修复：使用RedisService的hdel方法删除超时实例
                await this.redisService.hdel(businessKey, [instanceId], 'global');
                removedInstances++;
                this.logger.warn(`⏰ 移除超时的全局服务实例: ${instance.serviceName} (${instanceId})`);
                this.logger.warn(`   📍 从业务键删除: ${businessKey}`);
                this.logger.warn(`   ⏱️ 超时时间: ${Math.round((now - lastHeartbeat) / 1000)}秒 > ${Math.round(timeout / 1000)}秒`);
              } else {
                healthyInstances++;
              }
            } catch (parseError) {
              this.logger.warn(`⚠️ 解析实例数据失败: ${instanceId}`, parseError);
              // 删除无效的实例数据
              await this.redisService.hdel(businessKey, [instanceId], 'global');
              removedInstances++;
            }
          }
        } catch (keyError) {
          this.logger.warn(`⚠️ 处理业务键失败: ${businessKey}`, keyError);
        }
      }

      this.logger.debug(`🧹 全局服务健康检查完成: 总实例=${totalInstances}, 健康=${healthyInstances}, 超时移除=${removedInstances}`);
    } catch (error) {
      this.logger.error('❌ 全局服务健康检查失败:', error);
    }
  }
}

/**
 * 🚫 旧接口定义已移除
 *
 * 现在使用统一的服务实例接口：
 * - GlobalServiceInstance (来自 service-instance.interfaces.ts)
 * - GlobalServiceRegistrationRequest (来自 service-instance.interfaces.ts)
 *
 * 这些接口提供了更好的类型安全性和一致性
 */

// 为了向后兼容，保留类型别名
export type StoredGlobalServiceInstance = GlobalServiceInstance;