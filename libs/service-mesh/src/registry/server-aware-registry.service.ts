import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RedisService } from '@common/redis';
import { PortManager } from '@port-manager';
import { ServerAwareLoadBalancerService } from '../load-balancing/server-aware-load-balancer.service';
import {
  ServerAwareServiceInstance,
  ServerAwareRegistrationRequest,
  createServerAwareServiceInstance,
  validateServiceInstance,
} from '../interfaces/service-instance.interfaces';

/**
 * 🚫 旧接口定义已移除
 *
 * 现在使用统一的服务实例接口：
 * - ServerAwareServiceInstance (来自 service-instance.interfaces.ts)
 * - ServerAwareRegistrationRequest (来自 service-instance.interfaces.ts)
 *
 * 这些接口提供了更好的类型安全性和一致性
 */

// 为了向后兼容，保留类型别名
export type ServiceRegistrationRequest = ServerAwareRegistrationRequest;

/**
 * 区服感知的服务注册中心
 * 
 * 核心功能：
 * - 管理各区服的服务实例注册
 * - 提供区服级别的服务发现
 * - 支持区服级别的负载均衡
 * - 健康检查和故障转移
 */
@Injectable()
export class ServerAwareRegistryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServerAwareRegistryService.name);
  
  // 服务实例存储：Map<serviceName, Map<serverId, ServiceInstance[]>>
  private readonly instances = new Map<string, Map<string, ServerAwareServiceInstance[]>>();
  
  // 健康检查定时器
  private healthCheckInterval: NodeJS.Timeout | null = null;
  
  // 配置
  private readonly healthCheckIntervalMs: number;
  private readonly instanceTimeoutMs: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly redisService: RedisService,
    private readonly loadBalancer: ServerAwareLoadBalancerService,
  ) {
    this.healthCheckIntervalMs = this.configService.get<number>('gateway.serviceRegistry.healthCheckInterval', 30000);
    this.instanceTimeoutMs = this.configService.get<number>('gateway.serviceRegistry.instanceTimeout', 90000);
  }

  async onModuleInit() {
    this.logger.log('🚀 区服感知服务注册中心启动');
    
    // 启动健康检查
    this.startHealthCheck();
    
    // 输出配置信息
    this.logger.log(`健康检查间隔: ${this.healthCheckIntervalMs}ms`);
    this.logger.log(`实例超时时间: ${this.instanceTimeoutMs}ms`);
  }

  async onModuleDestroy() {
    this.logger.log('🛑 区服感知服务注册中心关闭');
    
    // 停止健康检查
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  }

  /**
   * 注册服务实例 - 使用新的统一接口
   */
  async registerInstance(request: ServerAwareRegistrationRequest): Promise<string> {
    const { serviceName, serverId, instanceName, host } = request;

    // 支持动态端口：优先使用请求端口，否则计算端口
    let port = request.port;
    let calculatedPort = false;

    if (!port) {
      // 动态计算端口
      port = PortManager.calculatePort(serviceName, serverId, request.instanceId || 0);
      calculatedPort = true;

      this.logger.log(`🔢 动态计算端口: ${serviceName}@${serverId} -> ${port}`);
    }

    // 验证端口可用性
    const isPortAvailable = await PortManager.validatePortAvailability(port);
    if (!isPortAvailable) {
      throw new Error(`端口 ${port} 不可用，服务: ${serviceName}@${serverId}`);
    }

    // 🚀 使用工厂函数创建标准化实例
    const instance = createServerAwareServiceInstance(
      {
        serviceName,
        serverId,
        instanceName,
        host,
        port, // 使用验证过的端口
        instanceId: request.instanceId, // 传递实例ID用于ID生成
        weight: request.weight,
        metadata: {
          ...request.metadata,
          // 增强元数据
          calculatedPort,
          portAllocationStrategy: calculatedPort ? 'dynamic' : 'static',
          basePort: calculatedPort ? PortManager.getBasePort(serviceName) : undefined,
          portValidated: true,
          portValidatedAt: new Date().toISOString(),
        },
      },
      {
        healthy: request.healthy ?? true,
      }
    );

    // 验证实例数据完整性
    const validationErrors = validateServiceInstance(instance);
    if (validationErrors.length > 0) {
      throw new Error(`Invalid service instance: ${validationErrors.join(', ')}`);
    }
    
    // 确保服务映射存在
    if (!this.instances.has(serviceName)) {
      this.instances.set(serviceName, new Map());
    }
    
    const serviceMap = this.instances.get(serviceName)!;
    
    // 确保区服映射存在
    if (!serviceMap.has(serverId)) {
      serviceMap.set(serverId, []);
    }
    
    const serverInstances = serviceMap.get(serverId)!;
    
    // 检查是否已存在相同实例名的实例
    const existingIndex = serverInstances.findIndex(inst => inst.instanceName === instanceName);
    if (existingIndex >= 0) {
      // 更新现有实例
      serverInstances[existingIndex] = instance;
      this.logger.log(`🔄 更新服务实例: ${instanceName} (${serviceName}@${serverId}) 端口: ${port}`);
    } else {
      // 添加新实例
      serverInstances.push(instance);
      this.logger.log(`注册服务实例: ${instanceName} (${serviceName}@${serverId}) 端口: ${port}`);
    }

    // 输出详细的注册信息
    this.logger.log(`📋 实例详情:`);
    this.logger.log(`   🏷️  实例ID: ${instance.id}`);
    this.logger.log(`   🌐 地址: ${host}:${port}`);
    this.logger.log(`   🔢 端口策略: ${calculatedPort ? '动态计算' : '固定配置'}`);
    if (calculatedPort) {
      this.logger.log(`   📊 基础端口: ${PortManager.getBasePort(serviceName)}`);
    }
    this.logger.log(`   端口验证: 通过`);
    this.logger.log(`   ⚖️  权重: ${instance.weight}`);
    this.logger.log(`   🏥 健康状态: ${instance.healthy ? '健康' : '不健康'}`);
    this.logger.log(`   📅 注册时间: ${instance.registeredAt.toISOString()}`);

    // 输出元数据信息
    if (Object.keys(instance.metadata).length > 0) {
      this.logger.log(`   📝 元数据: ${JSON.stringify(instance.metadata, null, 2)}`);
    }

    // 增强Redis同步逻辑
    try {
      const businessKey = `service_registry:instances:${serviceName}:${serverId}`;
      const instanceData = JSON.stringify({
        ...instance,
        // 添加端口相关信息到Redis存储
        portInfo: {
          port,
          calculatedPort,
          basePort: calculatedPort ? PortManager.getBasePort(serviceName) : undefined,
          validatedAt: new Date().toISOString(),
          allocationStrategy: calculatedPort ? 'dynamic' : 'static',
        }
      });

      await this.redisService.hset(businessKey, instanceName, instanceData, 'global');
      await this.redisService.expire(businessKey, 300, 'global');

      // 发布端口变更事件
      await this.redisService.publish('service_registry:events', JSON.stringify({
        type: 'instance_registered',
        serviceName,
        serverId,
        instanceName,
        instanceId: instance.id,
        port, // 包含端口信息
        calculatedPort,
        portAllocationStrategy: calculatedPort ? 'dynamic' : 'static',
        timestamp: new Date().toISOString(),
      }));

      this.logger.debug(`📡 已同步实例到Redis: ${instanceName} 端口: ${port}`);
    } catch (redisError) {
      this.logger.warn(`⚠️ Redis同步失败: ${redisError.message}`);
    }

    // 发出注册事件
    this.eventEmitter.emit('service.instance.registered', {
      serviceName,
      serverId,
      instance,
    });

    return instance.id;
  }

  /**
   * 注销服务实例
   */
  unregisterInstance(serviceName: string, serverId: string, instanceName: string): boolean {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return false;
    
    const serverInstances = serviceMap.get(serverId);
    if (!serverInstances) return false;
    
    const index = serverInstances.findIndex(inst => inst.instanceName === instanceName);
    if (index === -1) return false;
    
    const removedInstance = serverInstances.splice(index, 1)[0];
    
    this.logger.log(`🗑️ 注销服务实例: ${instanceName} (${serviceName}@${serverId})`);
    
    // 发出注销事件
    this.eventEmitter.emit('service.instance.unregistered', {
      serviceName,
      serverId,
      instance: removedInstance,
    });
    
    return true;
  }

  /**
   * 获取指定区服的服务实例
   */
  getInstances(serviceName: string, serverId: string): ServerAwareServiceInstance[] {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return [];

    const serverInstances = serviceMap.get(serverId);
    return serverInstances ? [...serverInstances] : [];
  }

  /**
   * 获取指定区服的健康服务实例
   */
  async getHealthyInstances(serviceName: string, serverId: string): Promise<ServerAwareServiceInstance[]> {
    // 🔧 修复：首先尝试从Redis全局存储获取最新的实例信息
    try {
      // 🔧 修复：使用标准的业务键名，不手动构造Redis键
      const businessKey = `service_registry:instances:${serviceName}:${serverId}`;
      const instancesData = await this.redisService.hgetall(businessKey, 'global');

      if (instancesData && Object.keys(instancesData).length > 0) {
        const redisInstances: ServerAwareServiceInstance[] = [];

        for (const [instanceName, instanceData] of Object.entries(instancesData)) {
          try {
            // Redis服务已经自动反序列化了数据，直接使用
            const instance = instanceData as any;
            // 转换日期字符串为Date对象
            instance.lastHealthCheck = new Date(instance.lastHealthCheck);
            instance.registeredAt = new Date(instance.registeredAt);
            redisInstances.push(instance);
          } catch (parseError) {
            this.logger.warn(`⚠️ 处理Redis实例数据失败: ${instanceName}`);
            this.logger.warn(`⚠️ 原始数据: ${JSON.stringify(instanceData)}`);
            this.logger.warn(`⚠️ 处理错误: ${parseError.message}`);
          }
        }

        // 返回健康的实例
        const healthyInstances = redisInstances.filter(instance => instance.healthy);
        this.logger.debug(`📡 从Redis全局存储获取健康实例: ${serviceName}@${serverId} (${healthyInstances.length}/${redisInstances.length})`);
        return healthyInstances;
      }
    } catch (redisError) {
      this.logger.warn(`⚠️ 从Redis获取实例失败: ${redisError.message}`);
    }

    // 如果Redis获取失败，回退到内存存储
    const instances = this.getInstances(serviceName, serverId);
    return instances.filter(instance => instance.healthy);
  }

  /**
   * 获取所有区服的服务实例
   */
  getAllInstances(serviceName: string): Map<string, ServerAwareServiceInstance[]> {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return new Map();
    
    const result = new Map<string, ServerAwareServiceInstance[]>();
    for (const [serverId, instances] of serviceMap) {
      result.set(serverId, [...instances]);
    }
    
    return result;
  }

  /**
   * 获取可用的区服列表
   */
  getAvailableServers(serviceName: string): string[] {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return [];
    
    const availableServers: string[] = [];
    for (const [serverId, instances] of serviceMap) {
      const healthyInstances = instances.filter(inst => inst.healthy);
      if (healthyInstances.length > 0) {
        availableServers.push(serverId);
      }
    }
    
    return availableServers;
  }

  /**
   * 🎯 选择服务实例（负载均衡）- 业务接口
   *
   * 🏗️ 架构设计：
   * - ✅ 获取健康实例列表
   * - ✅ 调用专业负载均衡器进行选择
   * - ✅ 管理连接数统计
   * - ✅ 提供完整的业务功能
   */
  async selectInstance(
    serviceName: string,
    serverId: string,
    strategy: string = 'round-robin',
    context?: any
  ): Promise<ServerAwareServiceInstance | null> {
    // 1. 获取健康实例列表
    const instances = await this.getHealthyInstances(serviceName, serverId);

    if (instances.length === 0) {
      this.logger.warn(`❌ 没有可用的健康实例: ${serviceName}@${serverId}`);
      return null;
    }

    // 2. 增强上下文信息
    const enhancedContext = {
      ...context,
      serviceName,
      serverId,
      counterKey: `${serviceName}-${serverId}`
    };

    // 3. 🚀 调用专业负载均衡器进行选择
    const selectedInstance = await this.loadBalancer.selectInstance(instances, {
      strategy,
      context: enhancedContext
    });

    if (!selectedInstance) {
      this.logger.warn(`❌ 负载均衡器未能选择实例: ${serviceName}@${serverId}`);
      return null;
    }

    // 4. 管理连接数统计
    this.incrementConnections(serviceName, serverId, selectedInstance.instanceName);

    this.logger.debug(`🎯 选择服务实例: ${selectedInstance.instanceName} (${serviceName}@${serverId}) 策略=${strategy}`);

    return selectedInstance;
  }

  /**
   * 🔓 释放实例连接 - 业务接口
   */
  releaseInstance(serviceName: string, serverId: string, instanceName: string): void {
    this.decrementConnections(serviceName, serverId, instanceName);
    this.logger.debug(`🔓 释放实例连接: ${instanceName} (${serviceName}@${serverId})`);
  }

  /**
   * 📋 获取可用的负载均衡策略 - 代理到负载均衡器
   */
  getAvailableLoadBalancingStrategies(): string[] {
    return this.loadBalancer.getAvailableStrategies();
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats(serviceName?: string): any {
    if (serviceName) {
      return this.getServiceStatsByName(serviceName);
    }
    
    const stats: any = {};
    for (const [name] of this.instances) {
      stats[name] = this.getServiceStatsByName(name);
    }
    
    return stats;
  }

  /**
   * 更新实例健康状态
   */
  updateInstanceHealth(serviceName: string, serverId: string, instanceName: string, healthy: boolean, responseTime?: number): boolean {
    const instances = this.getInstances(serviceName, serverId);
    const instance = instances.find(inst => inst.instanceName === instanceName);
    
    if (!instance) return false;
    
    const wasHealthy = instance.healthy;
    instance.healthy = healthy;
    instance.lastHealthCheck = new Date();
    
    if (responseTime !== undefined) {
      instance.responseTime = responseTime;
    }
    
    // 如果健康状态发生变化，发出事件
    if (wasHealthy !== healthy) {
      this.eventEmitter.emit('service.instance.health.changed', {
        serviceName,
        serverId,
        instanceName,
        healthy,
        instance,
      });
      
      this.logger.log(`🏥 实例健康状态变更: ${instanceName} (${serviceName}@${serverId}) -> ${healthy ? '健康' : '不健康'}`);
    }
    
    return true;
  }

  /**
   * 增加实例连接数
   */
  incrementConnections(serviceName: string, serverId: string, instanceName: string): boolean {
    const instances = this.getInstances(serviceName, serverId);
    const instance = instances.find(inst => inst.instanceName === instanceName);
    
    if (!instance) return false;
    
    instance.connections++;
    return true;
  }

  /**
   * 减少实例连接数
   */
  decrementConnections(serviceName: string, serverId: string, instanceName: string): boolean {
    const instances = this.getInstances(serviceName, serverId);
    const instance = instances.find(inst => inst.instanceName === instanceName);
    
    if (!instance) return false;
    
    instance.connections = Math.max(0, instance.connections - 1);
    return true;
  }

  // ==================== 私有方法 ====================

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckIntervalMs);
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const now = new Date();
    let totalInstances = 0;
    let healthyInstances = 0;
    let timeoutInstances = 0;
    let checkedInstances = 0;

    for (const [serviceName, serviceMap] of this.instances) {
      for (const [serverId, instances] of serviceMap) {
        for (let i = instances.length - 1; i >= 0; i--) {
          const instance = instances[i];
          totalInstances++;

          // 检查实例是否超时（超过超时时间没有更新健康检查时间）
          const timeSinceLastCheck = now.getTime() - instance.lastHealthCheck.getTime();
          if (timeSinceLastCheck > this.instanceTimeoutMs) {
            // 移除超时实例
            instances.splice(i, 1);
            timeoutInstances++;

            this.logger.warn(`⏰ 移除超时实例: ${instance.instanceName} (${serviceName}@${serverId})`);

            this.eventEmitter.emit('service.instance.timeout', {
              serviceName,
              serverId,
              instance,
            });
          } else {
            // 执行实际的健康检查
            try {
              const isHealthy = await this.checkInstanceHealth(instance);
              const wasHealthy = instance.healthy;

              // 更新健康状态和检查时间
              instance.healthy = isHealthy;
              instance.lastHealthCheck = now;
              checkedInstances++;

              if (isHealthy) {
                healthyInstances++;
              }

              // 如果健康状态发生变化，记录日志
              if (wasHealthy !== isHealthy) {
                this.logger.log(`🔄 实例健康状态变化: ${instance.instanceName} (${serviceName}@${serverId}) ${wasHealthy ? '健康' : '不健康'} -> ${isHealthy ? '健康' : '不健康'}`);
              }

            } catch (error) {
              // 健康检查失败，标记为不健康但不移除实例
              instance.healthy = false;
              instance.lastHealthCheck = now;
              checkedInstances++;

              this.logger.warn(`⚠️ 健康检查失败: ${instance.instanceName} (${serviceName}@${serverId}) - ${error.message}`);
            }
          }
        }
      }
    }

    if (totalInstances > 0) {
      this.logger.log(`🧹 健康检查完成: 总实例=${totalInstances}, 检查=${checkedInstances}, 健康=${healthyInstances}, 超时移除=${timeoutInstances}`);
    }
  }

  /**
   * 检查单个实例的健康状态
   */
  private async checkInstanceHealth(instance: ServerAwareServiceInstance): Promise<boolean> {
    try {
      const healthUrl = `http://${instance.host}:${instance.port}/health`;
      const startTime = Date.now();

      // 使用较短的超时时间进行健康检查
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

      const response = await fetch(healthUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      if (response.ok) {
        const healthData = await response.json();

        // 更新响应时间
        instance.responseTime = responseTime;

        // 检查健康状态
        return healthData.status === 'ok';
      } else {
        this.logger.warn(`⚠️ 健康检查HTTP错误: ${instance.instanceName} - ${response.status} ${response.statusText}`);
        return false;
      }

    } catch (error) {
      if (error.name === 'AbortError') {
        this.logger.warn(`⚠️ 健康检查超时: ${instance.instanceName}`);
      } else {
        this.logger.warn(`⚠️ 健康检查网络错误: ${instance.instanceName} - ${error.message}`);
      }
      return false;
    }
  }

  /**
   * 获取单个服务的统计信息
   */
  private getServiceStatsByName(serviceName: string): any {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) {
      return {
        totalServers: 0,
        totalInstances: 0,
        healthyInstances: 0,
        servers: {},
      };
    }
    
    let totalInstances = 0;
    let healthyInstances = 0;
    const servers: any = {};
    
    for (const [serverId, instances] of serviceMap) {
      const serverHealthyInstances = instances.filter(inst => inst.healthy).length;
      
      servers[serverId] = {
        totalInstances: instances.length,
        healthyInstances: serverHealthyInstances,
        instances: instances.map(inst => ({
          instanceName: inst.instanceName,
          healthy: inst.healthy,
          connections: inst.connections,
          responseTime: inst.responseTime,
          lastHealthCheck: inst.lastHealthCheck,
        })),
      };
      
      totalInstances += instances.length;
      healthyInstances += serverHealthyInstances;
    }
    
    return {
      totalServers: serviceMap.size,
      totalInstances,
      healthyInstances,
      servers,
    };
  }

  /**
   * 验证实例端口健康状态
   */
  async validateInstancePort(serviceName: string, serverId: string, instanceName: string): Promise<boolean> {
    const instances = this.instances.get(serviceName)?.get(serverId) || [];
    const instance = instances.find(inst => inst.instanceName === instanceName);

    if (!instance) {
      return false;
    }

    try {
      // 检查端口可用性
      const isAvailable = await PortManager.validatePortAvailability(instance.port);

      if (!isAvailable) {
        this.logger.warn(`⚠️ 实例端口不可用: ${instanceName} 端口: ${instance.port}`);

        // 标记实例为不健康
        instance.healthy = false;
        instance.lastHealthCheck = new Date();

        // 发出端口故障事件
        this.eventEmitter.emit('service.instance.port_failed', {
          serviceName,
          serverId,
          instanceName,
          port: instance.port,
        });
      }

      return isAvailable;
    } catch (error) {
      this.logger.error(`❌ 端口健康检查失败: ${instanceName}`, error);
      return false;
    }
  }

  /**
   * 批量验证多个实例的端口状态
   */
  async validateMultipleInstancePorts(serviceName: string, serverId: string): Promise<Map<string, boolean>> {
    const instances = this.instances.get(serviceName)?.get(serverId) || [];
    const results = new Map<string, boolean>();

    const promises = instances.map(async (instance) => {
      const isValid = await this.validateInstancePort(serviceName, serverId, instance.instanceName);
      results.set(instance.instanceName, isValid);
    });

    await Promise.all(promises);
    return results;
  }

  /**
   * 获取端口分配统计信息
   */
  getPortAllocationStats(): any {
    const stats = {
      totalInstances: 0,
      dynamicPorts: 0,
      staticPorts: 0,
      portRanges: new Map<string, { min: number; max: number; used: number[] }>(),
      services: new Map<string, any>(),
    };

    for (const [serviceName, serviceMap] of this.instances) {
      const serviceStats = {
        totalInstances: 0,
        dynamicPorts: 0,
        staticPorts: 0,
        usedPorts: [] as number[],
        servers: new Map<string, any>(),
      };

      for (const [serverId, instances] of serviceMap) {
        const serverStats = {
          instances: instances.length,
          dynamicPorts: 0,
          staticPorts: 0,
          ports: [] as number[],
        };

        instances.forEach(instance => {
          stats.totalInstances++;
          serviceStats.totalInstances++;

          const isDynamic = instance.metadata?.calculatedPort === true;
          if (isDynamic) {
            stats.dynamicPorts++;
            serviceStats.dynamicPorts++;
            serverStats.dynamicPorts++;
          } else {
            stats.staticPorts++;
            serviceStats.staticPorts++;
            serverStats.staticPorts++;
          }

          serviceStats.usedPorts.push(instance.port);
          serverStats.ports.push(instance.port);
        });

        serviceStats.servers.set(serverId, serverStats);
      }

      stats.services.set(serviceName, serviceStats);

      // 计算端口范围
      try {
        const range = PortManager.getPortRange(serviceName);
        stats.portRanges.set(serviceName, {
          min: range.min,
          max: range.max,
          used: serviceStats.usedPorts,
        });
      } catch (error) {
        // 忽略未知服务的端口范围
      }
    }

    return stats;
  }
}
