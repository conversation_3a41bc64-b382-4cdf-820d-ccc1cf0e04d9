// 核心服务
export * from './registry/server-aware-registry.service';
export * from './load-balancing/server-aware-load-balancer.service';

// 实例管理服务
export * from './lifecycle/instance-lifecycle.service';
export * from './orchestration/server-config-generator.service';
export * from './orchestration/container-orchestration.service';

// 自动注册服务
export * from './registry/service-auto-registration.service';

// 统一服务发现
export * from './discovery/unified-service-discovery.service';

// 全局服务注册
export * from './registry/global-service-registry.service';
export * from './registry/global-service-auto-registration.service';

// 模块
export * from './service-mesh.module';

// 🚀 统一架构相关导出
export * from './interfaces/service-options.interface';
export * from './utils/service-inference.util';
