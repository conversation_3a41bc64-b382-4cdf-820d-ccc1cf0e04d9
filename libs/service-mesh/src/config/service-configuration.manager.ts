import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { PortManager } from '@port-manager';
import { ServiceConfiguration } from '../interfaces/service-instance.interfaces';

/**
 * 🎯 优雅的服务配置管理器
 *
 * 核心特性：
 * 1. ✅ 唯一性保证：使用复合Key (serviceName:serverId:instanceIndex)
 * 2. ✅ 优雅易用：链式API + 智能默认值
 * 3. ✅ 架构正确：解决NestJS模块系统时序问题
 * 4. ✅ 类型安全：完整TypeScript支持
 * 5. ✅ 并发安全：防止重复初始化和竞态条件
 *
 * 🚀 优雅使用方式：
 * ```typescript
 * // 方式1：在main.ts中预初始化（推荐）
 * const config = await ServiceConfigurationManager
 *   .forService('character')
 *   .initialize(configService);
 * await app.listen(config.port);
 *
 * // 方式2：在app.module.ts中获取（自动从环境变量推断）
 * ServiceMeshModule.registerAsync({
 *   useFactory: (configService: ConfigService) => ({
 *     serviceName: 'character',
 *     preCalculatedConfig: ServiceConfigurationManager
 *       .forService('character')
 *       .getOrInitialize(configService)
 *   }),
 *   inject: [ConfigService]
 * })
 *
 * // 方式3：显式指定区服和实例
 * const config = await ServiceConfigurationManager
 *   .forService('character')
 *   .inServer('server_001')
 *   .withInstance(0)
 *   .initialize(configService);
 * ```
 */
export class ServiceConfigurationManager {
  private static readonly logger = new Logger(ServiceConfigurationManager.name);

  // 🔒 使用复合Key确保唯一性：serviceName:serverId:instanceIndex
  private static readonly configCache = new Map<string, ServiceConfiguration>();
  private static readonly initializationPromises = new Map<string, Promise<ServiceConfiguration>>();

  /**
   * 🎯 初始化服务配置（只在main.ts中调用一次）
   * 
   * 这是配置计算的唯一入口，确保配置只计算一次
   */
  static async initialize(
    serviceName: string, 
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration> = {}
  ): Promise<ServiceConfiguration> {
    
    // 🔒 防止重复初始化
    if (this.configCache.has(serviceName)) {
      this.logger.debug(`📦 使用缓存配置: ${serviceName}`);
      return this.configCache.get(serviceName)!;
    }

    // 🔒 防止并发初始化
    if (this.initializationPromises.has(serviceName)) {
      this.logger.debug(`⏳ 等待并发初始化完成: ${serviceName}`);
      return this.initializationPromises.get(serviceName)!;
    }

    // 🚀 开始初始化
    const initPromise = this.performInitialization(serviceName, configService, customOptions);
    this.initializationPromises.set(serviceName, initPromise);

    try {
      const config = await initPromise;
      this.configCache.set(serviceName, config);
      this.initializationPromises.delete(serviceName);
      
      this.logger.log(`✅ ${serviceName} 服务配置初始化完成`);
      return config;
      
    } catch (error) {
      this.initializationPromises.delete(serviceName);
      this.logger.error(`❌ ${serviceName} 服务配置初始化失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 🎯 获取已初始化的配置（在app.module.ts中调用）
   * 
   * 如果配置未初始化，抛出错误提示先调用initialize
   */
  static getConfig(serviceName: string): ServiceConfiguration {
    const config = this.configCache.get(serviceName);
    if (!config) {
      throw new Error(
        `🚨 服务配置未初始化: ${serviceName}\n` +
        `请先在main.ts中调用: await ServiceConfigurationManager.initialize('${serviceName}', configService)`
      );
    }
    
    this.logger.debug(`📦 获取缓存配置: ${serviceName}`);
    return config;
  }

  /**
   * 🔧 检查配置是否已初始化
   */
  static isInitialized(serviceName: string): boolean {
    return this.configCache.has(serviceName);
  }

  /**
   * 🧹 清除配置缓存（主要用于测试）
   */
  static clearCache(serviceName?: string): void {
    if (serviceName) {
      this.configCache.delete(serviceName);
      this.initializationPromises.delete(serviceName);
      this.logger.debug(`🧹 清除配置缓存: ${serviceName}`);
    } else {
      this.configCache.clear();
      this.initializationPromises.clear();
      this.logger.debug(`🧹 清除所有配置缓存`);
    }
  }

  /**
   * 📊 获取缓存统计信息
   */
  static getCacheStats(): { initialized: string[]; initializing: string[] } {
    return {
      initialized: Array.from(this.configCache.keys()),
      initializing: Array.from(this.initializationPromises.keys()),
    };
  }

  /**
   * 🔧 执行实际的配置初始化
   */
  private static async performInitialization(
    serviceName: string,
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration>
  ): Promise<ServiceConfiguration> {
    
    this.logger.log(`🔧 开始初始化 ${serviceName} 服务配置...`);

    try {
      // 1. 获取基础配置
      const serverId = this.getServerId(serviceName, configService, customOptions);
      const instanceIndex = this.getInstanceIndex(serviceName, configService, customOptions);
      const environment = this.getEnvironment(configService, customOptions);
      const host = this.getHost(customOptions);

      // 2. 计算端口（核心逻辑）
      const portResult = await this.calculatePort(serviceName, configService, serverId, instanceIndex, customOptions);

      // 3. 构建元数据
      const metadata = this.buildMetadata(environment, portResult.portCalculated, customOptions);

      // 4. 组装最终配置
      const configuration: ServiceConfiguration = {
        serviceName,
        serverId,
        instanceIndex,
        port: portResult.port,
        portCalculated: portResult.portCalculated,
        environment,
        host,
        metadata,
        // 应用自定义选项覆盖
        ...customOptions,
      };

      this.logConfiguration(configuration);
      return configuration;

    } catch (error) {
      this.logger.error(`❌ 初始化 ${serviceName} 服务配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取区服ID
   */
  private static getServerId(
    serviceName: string,
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration>
  ): string {
    const serverId = (
      customOptions.serverId ||
      configService.get('SERVER_ID') ||
      configService.get('DEFAULT_SERVER_ID') ||
      configService.get('CURRENT_SERVER_ID') ||
      configService.get(`${serviceName.toUpperCase()}_SERVER_ID`) ||
      'server_001'
    )?.trim();

    // 验证区服ID格式
    if (!/^server_\d{3}$/.test(serverId)) {
      this.logger.warn(`⚠️ 区服ID格式不标准: ${serverId}，建议使用 server_xxx 格式`);
    }

    return serverId;
  }

  /**
   * 获取实例序号
   */
  private static getInstanceIndex(
    serviceName: string,
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration>
  ): number {
    if (customOptions.instanceIndex !== undefined) {
      return customOptions.instanceIndex;
    }

    const instanceIndex = parseInt(
      configService.get('INSTANCE_INDEX') ||
      configService.get(`${serviceName.toUpperCase()}_INSTANCE_INDEX`) ||
      '0'
    );

    return instanceIndex;
  }

  /**
   * 获取运行环境
   */
  private static getEnvironment(
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration>
  ): string {
    return customOptions.environment || 
           configService.get('NODE_ENV', 'development');
  }

  /**
   * 获取主机地址
   */
  private static getHost(customOptions: Partial<ServiceConfiguration>): string {
    return customOptions.host || '127.0.0.1';
  }

  /**
   * 计算端口（核心逻辑）
   */
  private static async calculatePort(
    serviceName: string,
    configService: ConfigService,
    serverId: string,
    instanceIndex: number,
    customOptions: Partial<ServiceConfiguration>
  ): Promise<{ port: number; portCalculated: boolean }> {
    
    // 如果自定义选项中指定了端口，直接使用
    if (customOptions.port) {
      return { port: customOptions.port, portCalculated: false };
    }

    try {
      // 🎯 优先使用动态端口计算
      const dynamicPort = PortManager.calculatePort(serviceName, serverId, instanceIndex);
      this.logger.log(`🔢 动态计算端口: ${serviceName}@${serverId} 实例${instanceIndex} -> ${dynamicPort}`);
      
      return { port: dynamicPort, portCalculated: true };
      
    } catch (error) {
      // 🔄 降级到固定端口配置
      const fallbackPort = configService.get(`${serviceName.toUpperCase()}_PORT`, 3002);
      this.logger.warn(`⚠️ 端口计算失败，使用固定端口: ${fallbackPort}`);
      this.logger.warn(`   错误信息: ${error.message}`);
      
      return { port: fallbackPort, portCalculated: false };
    }
  }

  /**
   * 构建元数据
   */
  private static buildMetadata(
    environment: string, 
    portCalculated: boolean,
    customOptions: Partial<ServiceConfiguration>
  ): Record<string, any> {
    const baseMetadata = {
      version: '1.0.0',
      environment,
      startTime: new Date().toISOString(),
      portCalculated,
      configurationSource: 'ServiceConfigurationManager',
      initializationTime: new Date().toISOString(),
    };

    // 合并自定义元数据
    return { ...baseMetadata, ...customOptions.metadata };
  }

  /**
   * 记录配置信息
   */
  private static logConfiguration(config: ServiceConfiguration): void {
    this.logger.log(`✅ ${config.serviceName} 服务配置构建完成:`);
    this.logger.log(`   📋 服务名称: ${config.serviceName}`);
    this.logger.log(`   🏰 区服ID: ${config.serverId}`);
    this.logger.log(`   🔢 实例序号: ${config.instanceIndex}`);
    this.logger.log(`   🌐 监听地址: ${config.host}:${config.port}`);
    this.logger.log(`   🔧 端口计算: ${config.portCalculated ? '动态计算' : '固定配置'}`);
    this.logger.log(`   🌍 运行环境: ${config.environment}`);
    this.logger.log(`   📊 元数据字段: ${Object.keys(config.metadata).length}个`);
  }
}

// 🎯 向后兼容：保留原有的构建器接口
export class ServiceConfigurationBuilder {
  private serviceName: string;
  private customOptions: Partial<ServiceConfiguration> = {};

  private constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  static forService(serviceName: string): ServiceConfigurationBuilder {
    return new ServiceConfigurationBuilder(serviceName);
  }

  withOptions(options: Partial<ServiceConfiguration>): ServiceConfigurationBuilder {
    this.customOptions = { ...this.customOptions, ...options };
    return this;
  }

  async build(configService: ConfigService): Promise<ServiceConfiguration> {
    // 委托给管理器处理
    return ServiceConfigurationManager.initialize(this.serviceName, configService, this.customOptions);
  }
}
