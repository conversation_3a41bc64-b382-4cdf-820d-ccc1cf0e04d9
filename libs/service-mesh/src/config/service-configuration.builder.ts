import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { PortManager } from '@port-manager';
import { ServiceConfiguration } from '../interfaces/service-instance.interfaces';

/**
 * 服务配置管理器
 *
 * 真正优雅地解决配置重复计算问题：
 * 1. 全局单例模式，配置只计算一次
 * 2. 支持预初始化，在应用启动前完成配置
 * 3. main.ts和ServiceMeshModule共享同一配置实例
 * 4. 内置缓存机制，避免重复计算
 *
 * 使用示例：
 * ```typescript
 * // 在main.ts中预初始化
 * const config = await ServiceConfigurationManager.initialize('character', configService);
 * await app.listen(config.port);
 *
 * // 在app.module.ts中获取已初始化的配置
 * ServiceMeshModule.registerAsync({
 *   useFactory: () => ({
 *     serviceName: 'character',
 *     preCalculatedConfig: ServiceConfigurationManager.getConfig('character')
 *   })
 * })
 * ```
 */
export class ServiceConfigurationManager {
  private static readonly logger = new Logger(ServiceConfigurationManager.name);
  private static readonly configCache = new Map<string, ServiceConfiguration>();
  private static readonly initializationPromises = new Map<string, Promise<ServiceConfiguration>>();
  private readonly logger = new Logger(ServiceConfigurationBuilder.name);
  private serviceName: string;
  private customOptions: Partial<ServiceConfiguration> = {};

  private constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  /**
   * 创建服务配置构建器
   */
  static forService(serviceName: string): ServiceConfigurationBuilder {
    return new ServiceConfigurationBuilder(serviceName);
  }

  /**
   * 设置自定义选项（可选）
   */
  withOptions(options: Partial<ServiceConfiguration>): ServiceConfigurationBuilder {
    this.customOptions = { ...this.customOptions, ...options };
    return this;
  }

  /**
   * 构建服务配置
   * 
   * 🎯 核心优势：
   * - 一次性计算所有配置参数
   * - 统一的错误处理和日志记录
   * - 支持自定义选项覆盖
   * - 缓存计算结果避免重复
   */
  async build(configService: ConfigService): Promise<ServiceConfiguration> {
    this.logger.log(`🔧 开始构建 ${this.serviceName} 服务配置...`);

    try {
      // 1. 获取基础配置
      const serverId = this.getServerId(configService);
      const instanceIndex = this.getInstanceIndex(configService);
      const environment = this.getEnvironment(configService);
      const host = this.getHost(configService);

      // 2. 计算端口（核心逻辑）
      const portResult = await this.calculatePort(configService, serverId, instanceIndex);

      // 3. 构建元数据
      const metadata = this.buildMetadata(environment, portResult.portCalculated);

      // 4. 组装最终配置
      const configuration: ServiceConfiguration = {
        serviceName: this.serviceName,
        serverId,
        instanceIndex,
        port: portResult.port,
        portCalculated: portResult.portCalculated,
        environment,
        host,
        metadata,
        // 应用自定义选项覆盖
        ...this.customOptions,
      };

      this.logConfiguration(configuration);
      return configuration;

    } catch (error) {
      this.logger.error(`❌ 构建 ${this.serviceName} 服务配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取区服ID
   */
  private getServerId(configService: ConfigService): string {
    const serverId = (
      this.customOptions.serverId ||
      configService.get('SERVER_ID') ||
      configService.get('DEFAULT_SERVER_ID') ||
      configService.get('CURRENT_SERVER_ID') ||
      configService.get(`${this.serviceName.toUpperCase()}_SERVER_ID`) ||
      'server_001'
    )?.trim();

    // 验证区服ID格式
    if (!/^server_\d{3}$/.test(serverId)) {
      this.logger.warn(`⚠️ 区服ID格式不标准: ${serverId}，建议使用 server_xxx 格式`);
    }

    return serverId;
  }

  /**
   * 获取实例序号
   */
  private getInstanceIndex(configService: ConfigService): number {
    if (this.customOptions.instanceIndex !== undefined) {
      return this.customOptions.instanceIndex;
    }

    const instanceIndex = parseInt(
      configService.get('INSTANCE_INDEX') ||
      configService.get(`${this.serviceName.toUpperCase()}_INSTANCE_INDEX`) ||
      '0'
    );

    return instanceIndex;
  }

  /**
   * 获取运行环境
   */
  private getEnvironment(configService: ConfigService): string {
    return this.customOptions.environment || 
           configService.get('NODE_ENV', 'development');
  }

  /**
   * 获取主机地址
   */
  private getHost(configService: ConfigService): string {
    return this.customOptions.host || '127.0.0.1';
  }

  /**
   * 计算端口（核心逻辑）
   */
  private async calculatePort(
    configService: ConfigService,
    serverId: string,
    instanceIndex: number
  ): Promise<{ port: number; portCalculated: boolean }> {
    
    // 如果自定义选项中指定了端口，直接使用
    if (this.customOptions.port) {
      return { port: this.customOptions.port, portCalculated: false };
    }

    try {
      // 🎯 优先使用动态端口计算
      const dynamicPort = PortManager.calculatePort(this.serviceName, serverId, instanceIndex);
      this.logger.log(`🔢 动态计算端口: ${this.serviceName}@${serverId} 实例${instanceIndex} -> ${dynamicPort}`);
      
      // TODO: 可以在这里添加端口可用性检查
      // await this.validatePortAvailability(dynamicPort);
      
      return { port: dynamicPort, portCalculated: true };
      
    } catch (error) {
      // 🔄 降级到固定端口配置
      const fallbackPort = configService.get(`${this.serviceName.toUpperCase()}_PORT`, 3002);
      this.logger.warn(`⚠️ 端口计算失败，使用固定端口: ${fallbackPort}`);
      this.logger.warn(`   错误信息: ${error.message}`);
      
      return { port: fallbackPort, portCalculated: false };
    }
  }

  /**
   * 构建元数据
   */
  private buildMetadata(environment: string, portCalculated: boolean): Record<string, any> {
    const baseMetadata = {
      version: '1.0.0',
      environment,
      startTime: new Date().toISOString(),
      portCalculated,
      configurationSource: 'ServiceConfigurationBuilder',
      buildTime: new Date().toISOString(),
    };

    // 合并自定义元数据
    return { ...baseMetadata, ...this.customOptions.metadata };
  }

  /**
   * 记录配置信息
   */
  private logConfiguration(config: ServiceConfiguration): void {
    this.logger.log(`✅ ${config.serviceName} 服务配置构建完成:`);
    this.logger.log(`   📋 服务名称: ${config.serviceName}`);
    this.logger.log(`   🏰 区服ID: ${config.serverId}`);
    this.logger.log(`   🔢 实例序号: ${config.instanceIndex}`);
    this.logger.log(`   🌐 监听地址: ${config.host}:${config.port}`);
    this.logger.log(`   🔧 端口计算: ${config.portCalculated ? '动态计算' : '固定配置'}`);
    this.logger.log(`   🌍 运行环境: ${config.environment}`);
    this.logger.log(`   📊 元数据字段: ${Object.keys(config.metadata).length}个`);
  }
}
