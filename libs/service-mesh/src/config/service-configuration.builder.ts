import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { PortManager } from '@port-manager';
import { ServiceConfiguration } from '../interfaces/service-instance.interfaces';

/**
 * 🎯 优化的服务配置构建器
 *
 * 核心特性：
 * 1. ✅ 智能缓存：避免重复计算，提升性能
 * 2. ✅ 复合Key：支持分区分服架构 (serviceName-serverId-instanceIndex)
 * 3. ✅ 唯一性保证：解决多区服配置冲突问题
 * 4. ✅ 链式API：优雅的使用体验
 * 5. ✅ 并发安全：防止重复初始化
 *
 * 使用示例：
 * ```typescript
 * // 基础使用（自动从环境变量获取serverId和instanceIndex）
 * const config = await ServiceConfigurationBuilder
 *   .forService('character')
 *   .build(configService);
 *
 * // 显式指定区服和实例
 * const config = await ServiceConfigurationBuilder
 *   .forService('character')
 *   .withOptions({ serverId: 'server_002', instanceIndex: 1 })
 *   .build(configService);
 *
 * // 缓存机制：第二次调用直接返回缓存结果
 * const cachedConfig = await ServiceConfigurationBuilder
 *   .forService('character')
 *   .build(configService); // 🚀 从缓存获取，无需重复计算
 * ```
 */
export class ServiceConfigurationBuilder {
  private readonly logger = new Logger(ServiceConfigurationBuilder.name);
  private serviceName: string;
  private customOptions: Partial<ServiceConfiguration> = {};

  // 🔒 静态缓存：使用复合Key确保唯一性
  private static readonly configCache = new Map<string, ServiceConfiguration>();
  private static readonly initializationPromises = new Map<string, Promise<ServiceConfiguration>>();

  private constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  /**
   * 创建服务配置构建器
   */
  static forService(serviceName: string): ServiceConfigurationBuilder {
    return new ServiceConfigurationBuilder(serviceName);
  }

  /**
   * 设置自定义选项（可选）
   */
  withOptions(options: Partial<ServiceConfiguration>): ServiceConfigurationBuilder {
    this.customOptions = { ...this.customOptions, ...options };
    return this;
  }

  /**
   * 🎯 构建服务配置（支持智能缓存）
   *
   * 核心优势：
   * - 智能缓存：相同配置只计算一次
   * - 复合Key：支持分区分服架构
   * - 并发安全：防止重复初始化
   * - 统一错误处理和日志记录
   */
  async build(configService: ConfigService): Promise<ServiceConfiguration> {
    // 1. 预获取基础配置用于生成缓存Key
    const serverId = this.getServerId(configService);
    const instanceIndex = this.getInstanceIndex(configService);

    // 2. 生成复合缓存Key
    const cacheKey = this.generateCacheKey(serverId, instanceIndex);

    // 3. 🚀 尝试从缓存获取
    const cachedConfig = ServiceConfigurationBuilder.configCache.get(cacheKey);
    if (cachedConfig) {
      this.logger.debug(`📦 使用缓存配置: ${cacheKey}`);
      return cachedConfig;
    }

    // 4. 🔒 防止并发重复初始化
    const existingPromise = ServiceConfigurationBuilder.initializationPromises.get(cacheKey);
    if (existingPromise) {
      this.logger.debug(`⏳ 等待并发初始化完成: ${cacheKey}`);
      return existingPromise;
    }

    // 5. 🔧 开始新的配置构建
    const buildPromise = this.performBuild(configService, serverId, instanceIndex, cacheKey);
    ServiceConfigurationBuilder.initializationPromises.set(cacheKey, buildPromise);

    try {
      const configuration = await buildPromise;

      // 6. 💾 缓存结果
      ServiceConfigurationBuilder.configCache.set(cacheKey, configuration);
      ServiceConfigurationBuilder.initializationPromises.delete(cacheKey);

      this.logger.log(`✅ ${cacheKey} 配置构建完成并已缓存`);
      return configuration;

    } catch (error) {
      // 清理失败的初始化Promise
      ServiceConfigurationBuilder.initializationPromises.delete(cacheKey);
      this.logger.error(`❌ 构建 ${cacheKey} 服务配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 🔑 生成复合缓存Key
   * 格式：serviceName-serverId-instanceIndex
   * 示例：character-server_001-0
   */
  private generateCacheKey(serverId: string, instanceIndex: number): string {
    return `${this.serviceName}-${serverId}-${instanceIndex}`;
  }

  /**
   * 🔧 执行实际的配置构建
   */
  private async performBuild(
    configService: ConfigService,
    serverId: string,
    instanceIndex: number,
    cacheKey: string
  ): Promise<ServiceConfiguration> {

    this.logger.log(`🔧 开始构建 ${cacheKey} 服务配置...`);

    try {
      // 1. 获取其他配置
      const environment = this.getEnvironment(configService);
      const host = this.getHost(configService);

      // 2. 计算端口（核心逻辑）
      const portResult = await this.calculatePort(configService, serverId, instanceIndex);

      // 3. 构建元数据
      const metadata = this.buildMetadata(environment, portResult.portCalculated);

      // 4. 组装最终配置
      const configuration: ServiceConfiguration = {
        serviceName: this.serviceName,
        serverId,
        instanceIndex,
        port: portResult.port,
        portCalculated: portResult.portCalculated,
        environment,
        host,
        metadata,
        // 应用自定义选项覆盖
        ...this.customOptions,
      };

      this.logConfiguration(configuration);
      return configuration;

    } catch (error) {
      this.logger.error(`❌ 构建 ${cacheKey} 配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取区服ID
   */
  private getServerId(configService: ConfigService): string {
    const serverId = (
      this.customOptions.serverId ||
      configService.get('SERVER_ID') ||
      configService.get('DEFAULT_SERVER_ID') ||
      configService.get('CURRENT_SERVER_ID') ||
      configService.get(`${this.serviceName.toUpperCase()}_SERVER_ID`) ||
      'server_001'
    )?.trim();

    // 验证区服ID格式
    if (!/^server_\d{3}$/.test(serverId)) {
      this.logger.warn(`⚠️ 区服ID格式不标准: ${serverId}，建议使用 server_xxx 格式`);
    }

    return serverId;
  }

  /**
   * 获取实例序号
   */
  private getInstanceIndex(configService: ConfigService): number {
    if (this.customOptions.instanceIndex !== undefined) {
      return this.customOptions.instanceIndex;
    }

    const instanceIndex = parseInt(
      configService.get('INSTANCE_INDEX') ||
      configService.get(`${this.serviceName.toUpperCase()}_INSTANCE_INDEX`) ||
      '0'
    );

    return instanceIndex;
  }

  /**
   * 获取运行环境
   */
  private getEnvironment(configService: ConfigService): string {
    return this.customOptions.environment || 
           configService.get('NODE_ENV', 'development');
  }

  /**
   * 获取主机地址
   */
  private getHost(configService: ConfigService): string {
    return this.customOptions.host || '127.0.0.1';
  }

  /**
   * 计算端口（核心逻辑）
   */
  private async calculatePort(
    configService: ConfigService,
    serverId: string,
    instanceIndex: number
  ): Promise<{ port: number; portCalculated: boolean }> {
    
    // 如果自定义选项中指定了端口，直接使用
    if (this.customOptions.port) {
      return { port: this.customOptions.port, portCalculated: false };
    }

    try {
      // 🎯 优先使用动态端口计算
      const dynamicPort = PortManager.calculatePort(this.serviceName, serverId, instanceIndex);
      this.logger.log(`🔢 动态计算端口: ${this.serviceName}@${serverId} 实例${instanceIndex} -> ${dynamicPort}`);
      
      // TODO: 可以在这里添加端口可用性检查
      // await this.validatePortAvailability(dynamicPort);
      
      return { port: dynamicPort, portCalculated: true };
      
    } catch (error) {
      // 🔄 降级到固定端口配置
      const fallbackPort = configService.get(`${this.serviceName.toUpperCase()}_PORT`, 3002);
      this.logger.warn(`⚠️ 端口计算失败，使用固定端口: ${fallbackPort}`);
      this.logger.warn(`   错误信息: ${error.message}`);
      
      return { port: fallbackPort, portCalculated: false };
    }
  }

  /**
   * 构建元数据
   */
  private buildMetadata(environment: string, portCalculated: boolean): Record<string, any> {
    const baseMetadata = {
      version: '1.0.0',
      environment,
      startTime: new Date().toISOString(),
      portCalculated,
      configurationSource: 'ServiceConfigurationBuilder',
      buildTime: new Date().toISOString(),
    };

    // 合并自定义元数据
    return { ...baseMetadata, ...this.customOptions.metadata };
  }

  /**
   * 记录配置信息
   */
  private logConfiguration(config: ServiceConfiguration): void {
    this.logger.log(`✅ ${config.serviceName} 服务配置构建完成:`);
    this.logger.log(`   📋 服务名称: ${config.serviceName}`);
    this.logger.log(`   🏰 区服ID: ${config.serverId}`);
    this.logger.log(`   🔢 实例序号: ${config.instanceIndex}`);
    this.logger.log(`   🌐 监听地址: ${config.host}:${config.port}`);
    this.logger.log(`   🔧 端口计算: ${config.portCalculated ? '动态计算' : '固定配置'}`);
    this.logger.log(`   🌍 运行环境: ${config.environment}`);
    this.logger.log(`   📊 元数据字段: ${Object.keys(config.metadata).length}个`);
  }

  // ===== 🛠️ 静态缓存管理方法 =====

  /**
   * 🔍 检查配置是否已缓存
   */
  static isCached(serviceName: string, serverId: string, instanceIndex: number): boolean {
    const cacheKey = `${serviceName}-${serverId}-${instanceIndex}`;
    return this.configCache.has(cacheKey);
  }

  /**
   * 📦 获取缓存的配置（如果存在）
   */
  static getCachedConfig(serviceName: string, serverId: string, instanceIndex: number): ServiceConfiguration | null {
    const cacheKey = `${serviceName}-${serverId}-${instanceIndex}`;
    return this.configCache.get(cacheKey) || null;
  }

  /**
   * 🧹 清除指定配置的缓存
   */
  static clearCache(serviceName: string, serverId?: string, instanceIndex?: number): void {
    if (serverId !== undefined && instanceIndex !== undefined) {
      // 清除特定配置
      const cacheKey = `${serviceName}-${serverId}-${instanceIndex}`;
      this.configCache.delete(cacheKey);
      this.initializationPromises.delete(cacheKey);
      Logger.prototype.debug?.call(new Logger(ServiceConfigurationBuilder.name), `🧹 清除缓存: ${cacheKey}`);
    } else {
      // 清除服务的所有配置
      const keysToDelete: string[] = [];
      for (const key of this.configCache.keys()) {
        if (key.startsWith(`${serviceName}-`)) {
          keysToDelete.push(key);
        }
      }

      keysToDelete.forEach(key => {
        this.configCache.delete(key);
        this.initializationPromises.delete(key);
      });

      Logger.prototype.debug?.call(new Logger(ServiceConfigurationBuilder.name), `🧹 清除 ${serviceName} 服务的所有缓存 (${keysToDelete.length}个)`);
    }
  }

  /**
   * 🧹 清除所有缓存
   */
  static clearAllCache(): void {
    const cacheCount = this.configCache.size;
    this.configCache.clear();
    this.initializationPromises.clear();
    Logger.prototype.debug?.call(new Logger(ServiceConfigurationBuilder.name), `🧹 清除所有配置缓存 (${cacheCount}个)`);
  }

  /**
   * 📊 获取缓存统计信息
   */
  static getCacheStats(): {
    cached: string[];
    initializing: string[];
    totalCached: number;
    totalInitializing: number;
  } {
    return {
      cached: Array.from(this.configCache.keys()),
      initializing: Array.from(this.initializationPromises.keys()),
      totalCached: this.configCache.size,
      totalInitializing: this.initializationPromises.size,
    };
  }

  /**
   * 🎯 便捷方法：获取当前环境的配置（自动推断serverId和instanceIndex）
   */
  static async getCurrentConfig(serviceName: string, configService: ConfigService): Promise<ServiceConfiguration> {
    return ServiceConfigurationBuilder
      .forService(serviceName)
      .build(configService);
  }

  /**
   * 🌟 全局配置预初始化（解决模块加载时序问题）
   *
   * 在应用启动前预初始化配置，然后在模块中直接获取
   */
  private static globalConfigs = new Map<string, ServiceConfiguration>();

  static async preInitializeGlobalConfig(serviceName: string, configService: ConfigService): Promise<ServiceConfiguration> {
    const config = await ServiceConfigurationBuilder
      .forService(serviceName)
      .build(configService);

    // 存储到全局配置
    this.globalConfigs.set(serviceName, config);
    return config;
  }

  static getGlobalConfig(serviceName: string): ServiceConfiguration | null {
    return this.globalConfigs.get(serviceName) || null;
  }

  static hasGlobalConfig(serviceName: string): boolean {
    return this.globalConfigs.has(serviceName);
  }
}
