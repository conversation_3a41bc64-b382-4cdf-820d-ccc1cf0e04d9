import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { PortManager } from '@port-manager';
import { ServiceConfiguration } from '../interfaces/service-instance.interfaces';

/**
 * 🎯 简洁的服务配置构建器
 *
 * 核心特性：
 * 1. ✅ 智能缓存：避免重复计算，提升性能
 * 2. ✅ 复合Key：支持分区分服架构 (serviceName-serverId-instanceIndex)
 * 3. ✅ 唯一性保证：解决多区服配置冲突问题
 * 4. ✅ 简洁API：一个方法解决所有问题
 * 5. ✅ 并发安全：防止重复初始化
 *
 * 使用示例：
 * ```typescript
 * // 🎯 简洁的API - 自动从环境变量获取所有配置
 * const config = await ServiceConfigurationBuilder.getConfig('character', configService);
 *
 * // 🎯 支持自定义选项
 * const config = await ServiceConfigurationBuilder.getConfig('character', configService, {
 *   serverId: 'server_002',
 *   instanceIndex: 1,
 *   host: '*************'
 * });
 *
 * // 🚀 缓存机制：第二次调用直接返回缓存结果
 * const cachedConfig = await ServiceConfigurationBuilder.getConfig('character', configService);
 * ```
 */
export class ServiceConfigurationBuilder {
  private static readonly logger = new Logger(ServiceConfigurationBuilder.name);

  // 🔒 静态缓存：使用复合Key确保唯一性
  private static readonly configCache = new Map<string, ServiceConfiguration>();
  private static readonly initializationPromises = new Map<string, Promise<ServiceConfiguration>>();

  /**
   * 🎯 获取服务配置（简洁API）
   *
   * 核心优势：
   * - 智能缓存：相同配置只计算一次
   * - 复合Key：支持分区分服架构
   * - 并发安全：防止重复初始化
   * - 简洁易用：一个方法解决所有问题
   *
   * @param serviceName 服务名称
   * @param configService 配置服务
   * @param customOptions 自定义选项（可选）
   * @returns 服务配置
   */
  static async getConfig(
    serviceName: string,
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration> = {}
  ): Promise<ServiceConfiguration> {
    // 1. 预获取基础配置用于生成缓存Key
    const serverId = this.getServerId(serviceName, configService, customOptions);
    const instanceIndex = this.getInstanceIndex(serviceName, configService, customOptions);

    // 2. 生成复合缓存Key
    const cacheKey = this.generateCacheKey(serviceName, serverId, instanceIndex);

    // 3. 🚀 尝试从缓存获取
    const cachedConfig = this.configCache.get(cacheKey);
    if (cachedConfig) {
      this.logger.debug(`📦 使用缓存配置: ${cacheKey}`);
      return cachedConfig;
    }

    // 4. 🔒 防止并发重复初始化
    const existingPromise = this.initializationPromises.get(cacheKey);
    if (existingPromise) {
      this.logger.debug(`⏳ 等待并发初始化完成: ${cacheKey}`);
      return existingPromise;
    }

    // 5. 🔧 开始新的配置构建
    const buildPromise = this.performBuild(serviceName, configService, serverId, instanceIndex, customOptions, cacheKey);
    this.initializationPromises.set(cacheKey, buildPromise);

    try {
      const configuration = await buildPromise;

      // 6. 💾 缓存结果
      this.configCache.set(cacheKey, configuration);
      this.initializationPromises.delete(cacheKey);

      this.logger.log(`✅ ${cacheKey} 配置构建完成并已缓存`);
      return configuration;

    } catch (error) {
      // 清理失败的初始化Promise
      this.initializationPromises.delete(cacheKey);
      this.logger.error(`❌ 构建 ${cacheKey} 服务配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 🔑 生成复合缓存Key
   * 格式：serviceName-serverId-instanceIndex
   * 示例：character-server_001-0
   */
  private static generateCacheKey(serviceName: string, serverId: string, instanceIndex: number): string {
    return `${serviceName}-${serverId}-${instanceIndex}`;
  }

  /**
   * 🔧 执行实际的配置构建
   */
  private static async performBuild(
    serviceName: string,
    configService: ConfigService,
    serverId: string,
    instanceIndex: number,
    customOptions: Partial<ServiceConfiguration>,
    cacheKey: string
  ): Promise<ServiceConfiguration> {

    this.logger.log(`🔧 开始构建 ${cacheKey} 服务配置...`);

    try {
      // 1. 获取其他配置
      const environment = this.getEnvironment(configService, customOptions);
      const host = this.getHost(customOptions);

      // 2. 计算端口（核心逻辑）
      const portResult = await this.calculatePort(serviceName, configService, serverId, instanceIndex, customOptions);

      // 3. 构建元数据
      const metadata = this.buildMetadata(environment, portResult.portCalculated, customOptions);

      // 4. 组装最终配置
      const configuration: ServiceConfiguration = {
        serviceName,
        serverId,
        instanceIndex,
        port: portResult.port,
        portCalculated: portResult.portCalculated,
        environment,
        host,
        metadata,
        // 应用自定义选项覆盖
        ...customOptions,
      };

      this.logConfiguration(configuration);
      return configuration;

    } catch (error) {
      this.logger.error(`❌ 构建 ${cacheKey} 配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取区服ID
   */
  private static getServerId(
    serviceName: string,
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration>
  ): string {
    const serverId = (
      customOptions.serverId ||
      configService.get('SERVER_ID') ||
      configService.get('DEFAULT_SERVER_ID') ||
      configService.get('CURRENT_SERVER_ID') ||
      configService.get(`${serviceName.toUpperCase()}_SERVER_ID`) ||
      'server_001'
    )?.trim();

    // 验证区服ID格式
    if (!/^server_\d{3}$/.test(serverId)) {
      this.logger.warn(`⚠️ 区服ID格式不标准: ${serverId}，建议使用 server_xxx 格式`);
    }

    return serverId;
  }

  /**
   * 获取实例序号
   */
  private static getInstanceIndex(
    serviceName: string,
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration>
  ): number {
    if (customOptions.instanceIndex !== undefined) {
      return customOptions.instanceIndex;
    }

    const instanceIndex = parseInt(
      configService.get('INSTANCE_INDEX') ||
      configService.get(`${serviceName.toUpperCase()}_INSTANCE_INDEX`) ||
      '0'
    );

    return instanceIndex;
  }

  /**
   * 获取运行环境
   */
  private static getEnvironment(
    configService: ConfigService,
    customOptions: Partial<ServiceConfiguration>
  ): string {
    return customOptions.environment ||
           configService.get('NODE_ENV', 'development');
  }

  /**
   * 获取主机地址
   */
  private static getHost(customOptions: Partial<ServiceConfiguration>): string {
    return customOptions.host || '127.0.0.1';
  }

  /**
   * 计算端口（核心逻辑）
   */
  private static async calculatePort(
    serviceName: string,
    configService: ConfigService,
    serverId: string,
    instanceIndex: number,
    customOptions: Partial<ServiceConfiguration>
  ): Promise<{ port: number; portCalculated: boolean }> {

    // 如果自定义选项中指定了端口，直接使用
    if (customOptions.port) {
      return { port: customOptions.port, portCalculated: false };
    }

    try {
      // 🎯 优先使用动态端口计算
      const dynamicPort = PortManager.calculatePort(serviceName, serverId, instanceIndex);
      this.logger.log(`🔢 动态计算端口: ${serviceName}@${serverId} 实例${instanceIndex} -> ${dynamicPort}`);

      return { port: dynamicPort, portCalculated: true };

    } catch (error) {
      // 🔄 降级到固定端口配置
      const fallbackPort = configService.get(`${serviceName.toUpperCase()}_PORT`, 3002);
      this.logger.warn(`⚠️ 端口计算失败，使用固定端口: ${fallbackPort}`);
      this.logger.warn(`   错误信息: ${error.message}`);

      return { port: fallbackPort, portCalculated: false };
    }
  }

  /**
   * 构建元数据
   */
  private static buildMetadata(
    environment: string,
    portCalculated: boolean,
    customOptions: Partial<ServiceConfiguration>
  ): Record<string, any> {
    const baseMetadata = {
      version: '1.0.0',
      environment,
      startTime: new Date().toISOString(),
      portCalculated,
      configurationSource: 'ServiceConfigurationBuilder',
      buildTime: new Date().toISOString(),
    };

    // 合并自定义元数据
    return { ...baseMetadata, ...customOptions.metadata };
  }

  /**
   * 记录配置信息
   */
  private static logConfiguration(config: ServiceConfiguration): void {
    this.logger.log(`✅ ${config.serviceName} 服务配置构建完成:`);
    this.logger.log(`   📋 服务名称: ${config.serviceName}`);
    this.logger.log(`   🏰 区服ID: ${config.serverId}`);
    this.logger.log(`   🔢 实例序号: ${config.instanceIndex}`);
    this.logger.log(`   🌐 监听地址: ${config.host}:${config.port}`);
    this.logger.log(`   🔧 端口计算: ${config.portCalculated ? '动态计算' : '固定配置'}`);
    this.logger.log(`   🌍 运行环境: ${config.environment}`);
    this.logger.log(`   📊 元数据字段: ${Object.keys(config.metadata).length}个`);
  }

  // ===== 🛠️ 静态缓存管理方法 =====

  /**
   * 🔍 检查配置是否已缓存
   */
  static isCached(serviceName: string, serverId: string, instanceIndex: number): boolean {
    const cacheKey = this.generateCacheKey(serviceName, serverId, instanceIndex);
    return this.configCache.has(cacheKey);
  }

  /**
   * 📦 获取缓存的配置（如果存在）
   */
  static getCachedConfig(serviceName: string, serverId: string, instanceIndex: number): ServiceConfiguration | null {
    const cacheKey = this.generateCacheKey(serviceName, serverId, instanceIndex);
    return this.configCache.get(cacheKey) || null;
  }

  /**
   * 🧹 清除指定配置的缓存
   */
  static clearCache(serviceName: string, serverId?: string, instanceIndex?: number): void {
    if (serverId !== undefined && instanceIndex !== undefined) {
      // 清除特定配置
      const cacheKey = this.generateCacheKey(serviceName, serverId, instanceIndex);
      this.configCache.delete(cacheKey);
      this.initializationPromises.delete(cacheKey);
      this.logger.debug(`🧹 清除缓存: ${cacheKey}`);
    } else {
      // 清除服务的所有配置
      const keysToDelete: string[] = [];
      for (const key of this.configCache.keys()) {
        if (key.startsWith(`${serviceName}-`)) {
          keysToDelete.push(key);
        }
      }

      keysToDelete.forEach(key => {
        this.configCache.delete(key);
        this.initializationPromises.delete(key);
      });

      this.logger.debug(`🧹 清除 ${serviceName} 服务的所有缓存 (${keysToDelete.length}个)`);
    }
  }

  /**
   * 🧹 清除所有缓存
   */
  static clearAllCache(): void {
    const cacheCount = this.configCache.size;
    this.configCache.clear();
    this.initializationPromises.clear();
    this.logger.debug(`🧹 清除所有配置缓存 (${cacheCount}个)`);
  }

  /**
   * 📊 获取缓存统计信息
   */
  static getCacheStats(): {
    cached: string[];
    initializing: string[];
    totalCached: number;
    totalInitializing: number;
  } {
    return {
      cached: Array.from(this.configCache.keys()),
      initializing: Array.from(this.initializationPromises.keys()),
      totalCached: this.configCache.size,
      totalInitializing: this.initializationPromises.size,
    };
  }
}
