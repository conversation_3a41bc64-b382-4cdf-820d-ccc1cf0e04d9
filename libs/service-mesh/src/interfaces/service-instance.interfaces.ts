/**
 * 服务实例接口统一定义
 * 
 * 基于Composite设计模式和TypeScript最佳实践
 * 解决现有四个混乱接口的问题，提供类型安全的统一架构
 * 
 * <AUTHOR> Sonnet 4 深度分析
 * @version 2.0.0
 */

/**
 * 服务实例基础接口 - 所有服务实例的公共契约
 * 
 * 遵循Composite模式的Component接口设计原则：
 * - 定义所有子类的公共操作
 * - 提供统一的访问接口
 * - 支持类型安全的多态操作
 */
export interface BaseServiceInstance {
  // === 标识字段 ===
  /** 实例唯一标识符 - 统一使用id字段 */
  id: string;
  /** 服务名称 (如: character, hero, auth) */
  serviceName: string;
  
  // === 网络信息 ===
  /** 主机地址 */
  host: string;
  /** 端口号 */
  port: number;
  
  // === 状态信息 ===
  /** 健康状态 */
  healthy: boolean;
  /** 负载均衡权重 (默认: 1) */
  weight: number;
  
  // === 时间信息 (统一使用Date类型) ===
  /** 注册时间 */
  registeredAt: Date;
  /** 最后心跳时间 */
  lastHeartbeat: Date;
  
  // === 扩展信息 ===
  /** 元数据 - 支持自定义扩展 */
  metadata: Record<string, any>;
}

/**
 * 全局服务实例接口
 * 
 * 特点：
 * - 跨区服共享
 * - 无区服概念
 * - 简单的负载均衡需求
 */
export interface GlobalServiceInstance extends BaseServiceInstance {
  /** 服务类型标识 - 用于类型守卫 */
  readonly serviceType: 'global';
}

/**
 * 区服感知服务实例接口
 * 
 * 特点：
 * - 区服隔离
 * - 复杂的负载均衡统计
 * - 连接数和响应时间跟踪
 */
export interface ServerAwareServiceInstance extends BaseServiceInstance {
  /** 服务类型标识 - 用于类型守卫 */
  readonly serviceType: 'server-aware';
  
  // === 区服特有字段 ===
  /** 区服ID (如: server001, server002) */
  serverId: string;
  /** 实例名称 - 用于区分同服务多实例 (如: character-server001-1) */
  instanceName: string;
  
  // === 负载均衡统计字段 ===
  /** 当前连接数 */
  connections: number;
  /** 平均响应时间 (毫秒) */
  responseTime: number;
  /** 最后健康检查时间 */
  lastHealthCheck: Date;
}

/**
 * 通用服务实例联合类型
 * 
 * 用于需要处理多种服务类型的场景
 * 支持类型安全的判断和操作
 */
export type UniversalServiceInstance = GlobalServiceInstance | ServerAwareServiceInstance;

// ============================================================================
// 类型守卫函数 - 提供运行时类型安全
// ============================================================================

/**
 * 判断是否为全局服务实例
 */
export function isGlobalServiceInstance(
  instance: UniversalServiceInstance
): instance is GlobalServiceInstance {
  return instance.serviceType === 'global';
}

/**
 * 判断是否为区服感知服务实例
 */
export function isServerAwareServiceInstance(
  instance: UniversalServiceInstance
): instance is ServerAwareServiceInstance {
  return instance.serviceType === 'server-aware';
}

// ============================================================================
// 注册请求接口 - 输入数据传输对象
// ============================================================================

/**
 * 全局服务注册请求接口
 * 
 * 用于服务注册时的数据传输
 * 只包含必要的注册信息，其他字段由系统生成
 */
export interface GlobalServiceRegistrationRequest {
  serviceName: string;
  host: string;
  port: number;
  weight?: number;                    // 可选，默认1
  metadata?: Record<string, any>;     // 可选，默认{}
}

/**
 * 区服服务注册请求接口
 */
export interface ServerAwareRegistrationRequest {
  serviceName: string;
  serverId: string;
  instanceName: string;
  host: string;
  port?: number;                      // 可选，支持动态计算
  instanceId?: number;                // 可选，用于端口计算
  healthy?: boolean;                  // 可选，默认true
  weight?: number;                    // 可选，默认1
  metadata?: Record<string, any>;     // 可选，默认{}
}

// ============================================================================
// 工厂函数 - 创建标准化实例
// ============================================================================

/**
 * 创建全局服务实例
 */
export function createGlobalServiceInstance(
  request: GlobalServiceRegistrationRequest,
  options: {
    id?: string;
    registeredAt?: Date;
    lastHeartbeat?: Date;
    healthy?: boolean;
  } = {}
): GlobalServiceInstance {
  const now = new Date();
  
  return {
    id: options.id || generateInstanceId(request.serviceName, request.host, request.port),
    serviceName: request.serviceName,
    serviceType: 'global',
    host: request.host,
    port: request.port,
    healthy: options.healthy ?? true,
    weight: request.weight ?? 1,
    registeredAt: options.registeredAt || now,
    lastHeartbeat: options.lastHeartbeat || now,
    metadata: request.metadata || {},
  };
}

/**
 * 创建区服感知服务实例
 */
export function createServerAwareServiceInstance(
  request: ServerAwareRegistrationRequest,
  options: {
    id?: string;
    registeredAt?: Date;
    lastHeartbeat?: Date;
    lastHealthCheck?: Date;
    healthy?: boolean;
    connections?: number;
    responseTime?: number;
  } = {}
): ServerAwareServiceInstance {
  const now = new Date();
  
  return {
    id: options.id || generateInstanceId(request.serviceName, request.host, request.port),
    serviceName: request.serviceName,
    serviceType: 'server-aware',
    serverId: request.serverId,
    instanceName: request.instanceName,
    host: request.host,
    port: request.port,
    healthy: options.healthy ?? true,
    weight: request.weight ?? 1,
    connections: options.connections ?? 0,
    responseTime: options.responseTime ?? 0,
    registeredAt: options.registeredAt || now,
    lastHeartbeat: options.lastHeartbeat || now,
    lastHealthCheck: options.lastHealthCheck || now,
    metadata: request.metadata || {},
  };
}

/**
 * 生成全局服务实例ID
 * 格式: serviceName-host-port
 * 示例: auth-192-168-1-100-3001
 */
function generateGlobalInstanceId(serviceName: string, host: string, port: number): string {
  const normalizedHost = host.replace(/\./g, '-').replace(/:/g, '-');
  return `${serviceName}-${normalizedHost}-${port}`;
}

/**
 * 生成区服服务实例ID
 * 格式: instanceName-instanceId-host-port
 * 示例: character-server_001-1-192-168-1-100-3210
 */
function generateServerAwareInstanceId(
  instanceName: string,
  instanceId: number,
  host: string,
  port: number
): string {
  const normalizedHost = host.replace(/\./g, '-').replace(/:/g, '-');
  return `${instanceName}-${instanceId}-${normalizedHost}-${port}`;
}

// ============================================================================
// 验证函数 - 确保数据完整性
// ============================================================================

/**
 * 验证服务实例数据完整性
 */
export function validateServiceInstance(instance: UniversalServiceInstance): string[] {
  const errors: string[] = [];
  
  // 基础字段验证
  if (!instance.id) errors.push('id is required');
  if (!instance.serviceName) errors.push('serviceName is required');
  if (!instance.host) errors.push('host is required');
  if (!instance.port || instance.port <= 0) errors.push('port must be positive');
  if (instance.weight < 0) errors.push('weight must be non-negative');
  
  // 区服特有字段验证
  if (isServerAwareServiceInstance(instance)) {
    if (!instance.serverId) errors.push('serverId is required for server-aware service');
    if (!instance.instanceName) errors.push('instanceName is required for server-aware service');
    if (instance.connections < 0) errors.push('connections must be non-negative');
    if (instance.responseTime < 0) errors.push('responseTime must be non-negative');
  }
  
  return errors;
}

/**
 * 断言服务实例有效性
 */
export function assertValidServiceInstance(instance: UniversalServiceInstance): void {
  const errors = validateServiceInstance(instance);
  if (errors.length > 0) {
    throw new Error(`Invalid service instance: ${errors.join(', ')}`);
  }
}
