import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { RedisService } from './redis.service';
import { DataType } from './types/redis.types';

export interface QueueJobData {
  id: string;
  type: string;
  payload: any;
  priority?: number;
  delay?: number;
  attempts?: number;
  createdAt: Date;
  metadata?: Record<string, any>;
}

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
}

export interface QueueOptions {
  maxRetries?: number;
  retryDelay?: number;
  priority?: number;
  delay?: number;
  ttl?: number;

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

export interface JobResult {
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
  completedAt: Date;
}

@Injectable()
export class RedisQueueService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisQueueService.name);
  private readonly queues = new Map<string, any>();
  private readonly stats = new Map<string, QueueStats>();

  constructor(private readonly redisService: RedisService) {}

  async onModuleDestroy() {
    await this.cleanup();
  }

  // ==================== 通用队列操作 ====================

  /**
   * 添加任务到队列 
   */
  async addJob(
    queueName: string,
    jobType: string,
    payload: any,
    options: QueueOptions = {}
  ): Promise<string> {
    const jobId = this.generateJobId(jobType);
    const jobData: QueueJobData = {
      id: jobId,
      type: jobType,
      payload,
      priority: options.priority || 5,
      delay: options.delay || 0,
      attempts: options.maxRetries || 3,
      createdAt: new Date(),
      metadata: {
        queueName,
        dataType: options.dataType || 'server',
        addedAt: Date.now(),
      },
    };

    const queueKey = this.getQueueKey(queueName, options.dataType);

    // 使用 Redis 列表作为队列 - 传递dataType
    await this.redisService.lpush(queueKey, jobData, options.dataType, options.serverId);

    // 如果有延迟，使用有序集合
    if (options.delay && options.delay > 0) {
      const delayedKey = this.getDelayedQueueKey(queueName, options.dataType);
      const executeAt = Date.now() + options.delay;
      await this.redisService.zadd(delayedKey, executeAt, jobId, options.dataType, options.serverId);
    }

    // 设置任务数据
    const jobKey = this.getJobKey(jobId, options.dataType);
    await this.redisService.set(jobKey, jobData, options.ttl || 86400, options.dataType, options.serverId);

    this.logger.debug(`Job added to queue ${queueName}: ${jobId} (dataType: ${options.dataType || 'server'})`);
    return jobId;
  }

  /**
   * 获取队列中的下一个任务 
   */
  async getNextJob(
    queueName: string,
    timeout: number = 0,
    dataType?: DataType,
    serverId?: string
  ): Promise<QueueJobData | null> {
    const queueKey = this.getQueueKey(queueName, dataType);

    try {
      let result;
      if (timeout > 0) {
        // 阻塞式获取 - 需要使用完整键名
        const fullKey = this.redisService.buildDataTypeKey(queueKey, dataType, serverId);
        result = await this.redisService.getClient().brpop(fullKey, timeout);
      } else {
        // 非阻塞式获取
        result = await this.redisService.rpop(queueKey, dataType, serverId);
      }

      if (result) {
        const jobData = Array.isArray(result) ? result[1] : result;
        return typeof jobData === 'string' ? JSON.parse(jobData) : jobData;
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to get next job from queue ${queueName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 批量添加任务
   */
  async addBatchJobs(
    queueName: string,
    jobs: Array<{ type: string; payload: any; options?: QueueOptions }>
  ): Promise<string[]> {
    const jobIds: string[] = [];

    for (const job of jobs) {
      const jobId = await this.addJob(queueName, job.type, job.payload, job.options);
      jobIds.push(jobId);
    }

    this.logger.log(`Added ${jobs.length} jobs to queue ${queueName}`);
    return jobIds;
  }

  /**
   * 获取队列统计信息 
   */
  async getQueueStats(queueName: string, dataType?: DataType, serverId?: string): Promise<QueueStats> {
    const queueKey = this.getQueueKey(queueName, dataType);
    const delayedKey = this.getDelayedQueueKey(queueName, dataType);
    const processingKey = this.getProcessingQueueKey(queueName, dataType);
    const failedKey = this.getFailedQueueKey(queueName, dataType);
    const completedKey = this.getCompletedQueueKey(queueName, dataType);

    const [waiting, delayed, active, failed, completed] = await Promise.all([
      this.redisService.llen(queueKey, dataType, serverId),
      this.redisService.getClient().zcard(this.redisService.buildDataTypeKey(delayedKey, dataType, serverId)),
      this.redisService.llen(processingKey, dataType, serverId),
      this.redisService.llen(failedKey, dataType, serverId),
      this.redisService.llen(completedKey, dataType, serverId),
    ]);

    return {
      waiting,
      active,
      completed,
      failed,
      delayed,
      paused: await this.isQueuePaused(queueName, dataType, serverId),
    };
  }

  /**
   * 暂停队列 
   */
  async pauseQueue(queueName: string, dataType?: DataType, serverId?: string): Promise<void> {
    const pauseKey = this.getPauseKey(queueName, dataType);
    await this.redisService.set(pauseKey, 'paused', 0, dataType, serverId); // 永不过期
    this.logger.log(`Queue ${queueName} paused (dataType: ${dataType || 'server'})`);
  }

  /**
   * 恢复队列 
   */
  async resumeQueue(queueName: string, dataType?: DataType, serverId?: string): Promise<void> {
    const pauseKey = this.getPauseKey(queueName, dataType);
    await this.redisService.del(pauseKey, dataType, serverId);
    this.logger.log(`Queue ${queueName} resumed (dataType: ${dataType || 'server'})`);
  }

  /**
   * 检查队列是否暂停 
   */
  async isQueuePaused(queueName: string, dataType?: DataType, serverId?: string): Promise<boolean> {
    const pauseKey = this.getPauseKey(queueName, dataType);
    return await this.redisService.exists(pauseKey, dataType, serverId);
  }

  /**
   * 处理延迟任务 
   */
  async processDelayedJobs(queueName: string, dataType?: DataType, serverId?: string): Promise<number> {
    const delayedKey = this.getDelayedQueueKey(queueName, dataType);
    const queueKey = this.getQueueKey(queueName, dataType);
    const now = Date.now();

    // 获取到期的延迟任务 - 使用完整键名
    const fullDelayedKey = this.redisService.buildDataTypeKey(delayedKey, dataType, serverId);
    const expiredJobs = await this.redisService.getClient().zrangebyscore(
      fullDelayedKey,
      0,
      now,
      'WITHSCORES'
    );

    let processedCount = 0;
    for (let i = 0; i < expiredJobs.length; i += 2) {
      const jobId = expiredJobs[i];
      const jobKey = this.getJobKey(jobId, dataType);
      const jobData = await this.redisService.get(jobKey, dataType, serverId);

      if (jobData) {
        // 移动到主队列
        await this.redisService.lpush(queueKey, jobData, dataType, serverId);
        await this.redisService.getClient().zrem(fullDelayedKey, jobId);
        processedCount++;
      }
    }

    if (processedCount > 0) {
      this.logger.debug(`Processed ${processedCount} delayed jobs for queue ${queueName} (dataType: ${dataType || 'server'})`);
    }

    return processedCount;
  }

  /**
   * 标记任务为失败 
   */
  async markJobFailed(jobId: string, error: string, dataType?: DataType, serverId?: string): Promise<void> {
    const jobKey = this.getJobKey(jobId, dataType);
    const jobData = await this.redisService.get<QueueJobData>(jobKey, dataType, serverId);

    if (jobData) {
      const queueDataType = jobData.metadata?.dataType as DataType || dataType;
      const failedKey = this.getFailedQueueKey(jobData.metadata?.queueName || 'default', queueDataType);
      const failedJob = {
        ...jobData,
        error,
        failedAt: new Date(),
      };

      await this.redisService.lpush(failedKey, failedJob, queueDataType, serverId);
      this.logger.warn(`Job ${jobId} marked as failed: ${error} (dataType: ${queueDataType || 'server'})`);
    }
  }

  /**
   * 标记任务为完成 
   */
  async markJobCompleted(jobId: string, result?: any, dataType?: DataType, serverId?: string): Promise<void> {
    const jobKey = this.getJobKey(jobId, dataType);
    const jobData = await this.redisService.get<QueueJobData>(jobKey, dataType, serverId);

    if (jobData) {
      const queueDataType = jobData.metadata?.dataType as DataType || dataType;
      const completedKey = this.getCompletedQueueKey(jobData.metadata?.queueName || 'default', queueDataType);
      const completedJob = {
        ...jobData,
        result,
        completedAt: new Date(),
      };

      await this.redisService.lpush(completedKey, completedJob, queueDataType, serverId);
      await this.redisService.del(jobKey, dataType, serverId); // 清理任务数据
      this.logger.debug(`Job ${jobId} marked as completed (dataType: ${queueDataType || 'server'})`);
    }
  }

  /**
   * 获取失败的任务 
   */
  async getFailedJobs(queueName: string, start = 0, end = 10, dataType?: DataType, serverId?: string): Promise<QueueJobData[]> {
    const failedKey = this.getFailedQueueKey(queueName, dataType);
    return await this.redisService.lrange(failedKey, start, end, dataType, serverId);
  }

  /**
   * 重试失败的任务 
   */
  async retryFailedJob(queueName: string, jobId: string, dataType?: DataType, serverId?: string): Promise<boolean> {
    const failedKey = this.getFailedQueueKey(queueName, dataType);
    const queueKey = this.getQueueKey(queueName, dataType);

    // 从失败队列中查找任务
    const failedJobs = await this.redisService.lrange(failedKey, 0, -1, dataType, serverId);
    const jobIndex = failedJobs.findIndex((job: any) => job.id === jobId);

    if (jobIndex !== -1) {
      const jobData = failedJobs[jobIndex];

      // 移除错误信息，重新添加到队列
      delete (jobData as any).error;
      delete (jobData as any).failedAt;

      await this.redisService.lpush(queueKey, jobData, dataType, serverId);

      // 从失败队列中移除（这里简化处理，实际可能需要更复杂的逻辑）
      const fullFailedKey = this.redisService.buildDataTypeKey(failedKey, dataType, serverId);
      await this.redisService.getClient().lrem(fullFailedKey, 1, JSON.stringify(failedJobs[jobIndex]));

      this.logger.log(`Job ${jobId} retried in queue ${queueName} (dataType: ${dataType || 'server'})`);
      return true;
    }

    return false;
  }

  /**
   * 清理队列中的旧任务 
   */
  async cleanQueue(queueName: string, maxAge: number = 86400000, dataType?: DataType, serverId?: string): Promise<number> {
    const completedKey = this.getCompletedQueueKey(queueName, dataType);
    const failedKey = this.getFailedQueueKey(queueName, dataType);
    const cutoffTime = Date.now() - maxAge;

    let cleanedCount = 0;

    // 清理已完成的任务
    const completedJobs = await this.redisService.lrange(completedKey, 0, -1, dataType, serverId);
    const fullCompletedKey = this.redisService.buildDataTypeKey(completedKey, dataType, serverId);
    for (const job of completedJobs) {
      if ((job as any).completedAt && new Date((job as any).completedAt).getTime() < cutoffTime) {
        await this.redisService.getClient().lrem(fullCompletedKey, 1, JSON.stringify(job));
        cleanedCount++;
      }
    }

    // 清理失败的任务
    const failedJobs = await this.redisService.lrange(failedKey, 0, -1, dataType, serverId);
    const fullFailedKey = this.redisService.buildDataTypeKey(failedKey, dataType, serverId);
    for (const job of failedJobs) {
      if ((job as any).failedAt && new Date((job as any).failedAt).getTime() < cutoffTime) {
        await this.redisService.getClient().lrem(fullFailedKey, 1, JSON.stringify(job));
        cleanedCount++;
      }
    }

    this.logger.log(`Cleaned ${cleanedCount} old jobs from queue ${queueName} (dataType: ${dataType || 'server'})`);
    return cleanedCount;
  }

  // ==================== 私有方法 ====================

  private generateJobId(type: string): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 构建队列键名 
   * 注意：队列操作通常是全局性的，但为了支持分区分服，这里提供dataType支持
   */
  private getQueueKey(queueName: string, dataType?: DataType): string {
    // 队列键名包含dataType信息，用于区分不同类型的队列
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}queue:${queueName}`;
  }

  private getDelayedQueueKey(queueName: string, dataType?: DataType): string {
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}queue:${queueName}:delayed`;
  }

  private getProcessingQueueKey(queueName: string, dataType?: DataType): string {
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}queue:${queueName}:processing`;
  }

  private getFailedQueueKey(queueName: string, dataType?: DataType): string {
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}queue:${queueName}:failed`;
  }

  private getCompletedQueueKey(queueName: string, dataType?: DataType): string {
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}queue:${queueName}:completed`;
  }

  private getPauseKey(queueName: string, dataType?: DataType): string {
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}queue:${queueName}:paused`;
  }

  private getJobKey(jobId: string, dataType?: DataType): string {
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}job:${jobId}`;
  }

  private async cleanup(): Promise<void> {
    this.logger.log('Redis queue service cleanup completed');
  }


}
