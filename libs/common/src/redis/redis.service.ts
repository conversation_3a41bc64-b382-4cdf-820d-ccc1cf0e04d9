import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { DataType } from './types/redis.types';
import { RedisKeyUtils } from './utils/redis-key.utils';

export enum CacheTTL {
  SHORT = 300,      // 5分钟
  MEDIUM = 1800,    // 30分钟
  LONG = 3600,      // 1小时
  DAILY = 86400,    // 24小时
  WEEKLY = 604800,  // 7天
}

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private redis: Redis;
  private subscriber: Redis;
  private healthStatus: 'initializing' | 'ready' | 'error' = 'initializing';
  private initializationPromise: Promise<void>;
  private serviceContext: string;
  private redisConfig: any;

  constructor(
    config?: any,
    serviceContext?: string,
    private readonly injectedServerId?: string
  ) {
    this.serviceContext = serviceContext || 'unknown';
    this.redisConfig = config;
    this.logger.log(`Redis service instance created for service: ${this.serviceContext} with prefix: ${config?.keyPrefix || 'none'}`);
  }

  async onModuleInit() {
    this.initializationPromise = this.initializeConnection();
    await this.initializationPromise;
  }

  private async initializeConnection(): Promise<void> {
    try {
      this.logger.log(`开始初始化Redis连接... 服务: ${this.serviceContext}`);

      // 使用传入的配置或默认配置
      const config = this.redisConfig || this.getDefaultRedisConfig();

      // 确保立即连接，不延迟
      const finalConfig = {
        ...config,
        lazyConnect: false,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
      };

      this.logger.log(`Redis配置: ${JSON.stringify({
        host: finalConfig.host,
        port: finalConfig.port,
        db: finalConfig.db,
        keyPrefix: finalConfig.keyPrefix
      })}`);

      this.redis = new Redis(finalConfig);
      this.subscriber = this.redis.duplicate();

      // 测试连接
      await this.redis.ping();
      this.healthStatus = 'ready';

      this.logger.log('Redis连接建立成功');

      // 监听连接事件
      this.redis.on('connect', () => {
        this.healthStatus = 'ready';
        this.logger.log('Redis connected');
      });

      this.redis.on('error', (error) => {
        this.healthStatus = 'error';
        this.logger.error(`Redis error: ${error.message}`);
      });

      this.redis.on('close', () => {
        this.healthStatus = 'error';
        this.logger.warn('Redis connection closed');
      });

      this.redis.on('reconnecting', () => {
        this.healthStatus = 'initializing';
        this.logger.log('Redis reconnecting...');
      });

    } catch (error) {
      this.healthStatus = 'error';
      this.logger.error('Redis连接初始化失败:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    if (this.redis) {
      await this.redis.quit();
    }
    if (this.subscriber) {
      await this.subscriber.quit();
    }
    this.logger.log('Redis connections closed');
  }

  /**
   * 移除所有运行时健康检查，因为：
   * 1.启动时已经保证了连接
   * 2.ioredis有自动重连机制
   * 3.运行时检查是多余的性能开销
   * 确保Redis服务就绪
   */
  async ensureReady(): Promise<void> {
    if (this.healthStatus === 'ready') {
      return;
    }

    if (this.healthStatus === 'initializing') {
      // 等待初始化完成
      if (this.initializationPromise) {
        await this.initializationPromise;
      }
      return;
    }

    if (this.healthStatus === 'error') {
      throw new Error('Redis service is not available');
    }
  }

  /**
   * 检查Redis连接状态
   */
  isReady(): boolean {
    return this.healthStatus === 'ready';
  }

  /**
   * 获取健康状态
   */
  getHealthStatus(): 'initializing' | 'ready' | 'error' {
    return this.healthStatus;
  }

  /**
   * 等待Redis初始化完成
   */
  async waitForInitialization(): Promise<void> {
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  // ==================== 基础操作 ====================

  /**
   * 构建带数据类型的完整键名 - 统一使用工具类
   */
  public buildDataTypeKey(key: string, dataType?: DataType, serverId?: string): string {
    // const keyPrefix = this.redisConfig?.keyPrefix || '';
    // 🔧 修复双重前缀问题：ioredis已经处理了连接级前缀，这里只处理业务逻辑
    // 不再添加环境和项目前缀，只处理dataType和service的业务逻辑
    const keyPrefix = '';

    const serviceContext = this.getServiceContext();

    return RedisKeyUtils.buildDataTypeKey(keyPrefix, key, {
      dataType, // 工具类中已处理默认值逻辑
      serverId,
      serviceContext,
    });
  }

  /**
   * 获取当前区服ID
   * 优先级：注入的serverId > 工具类获取
   */
  private getCurrentServerId(): string {
    return this.injectedServerId || RedisKeyUtils.getCurrentServerId();
  }

  // ==================== 新增工具方法 ====================

  /**
   * 获取服务上下文
   */
  private getServiceContext(): string {
    // 使用注入的服务上下文，回退到环境变量
    return this.serviceContext || process.env.MICROSERVICE_NAME || 'default';
  }

  // ==================== 便捷方法 ====================

  /**
   * 获取全局数据（便捷方法）
   */
  async getGlobal<T>(key: string): Promise<T | null> {
    return this.get<T>(key, 'global');
  }

  /**
   * 设置全局数据（便捷方法）
   */
  async setGlobal<T>(key: string, value: T, ttl?: number): Promise<void> {
    return this.set(key, value, ttl, 'global');
  }

  /**
   * 获取跨服数据（便捷方法）
   */
  async getCross<T>(key: string): Promise<T | null> {
    return this.get<T>(key, 'cross');
  }

  /**
   * 设置跨服数据（便捷方法）
   */
  async setCross<T>(key: string, value: T, ttl?: number): Promise<void> {
    return this.set(key, value, ttl, 'cross');
  }

  /**
   * 获取指定区服数据（便捷方法）
   */
  async getFromServer<T>(key: string, serverId: string): Promise<T | null> {
    return this.get<T>(key, 'server', serverId);
  }

  /**
   * 设置指定区服数据（便捷方法）
   */
  async setToServer<T>(key: string, value: T, serverId: string, ttl?: number): Promise<void> {
    return this.set(key, value, ttl, 'server', serverId);
  }

  /**
   * 设置键值对 
   */
  async set(key: string, value: any, ttl?: number, dataType?: DataType, serverId?: string): Promise<void> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const serializedValue = this.serialize(value);

      if (ttl) {
        await this.redis.setex(fullKey, ttl, serializedValue);
      } else {
        await this.redis.set(fullKey, serializedValue);
      }

      this.logger.debug(`Set key: ${fullKey} (dataType: ${dataType || 'server'})`);
    } catch (error) {
      this.logger.error(`Failed to set key ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取值 
   */
  async get<T>(key: string, dataType?: DataType, serverId?: string): Promise<T | null> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const value = await this.redis.get(fullKey);
      return value ? this.deserialize<T>(value) : null;
    } catch (error) {
      this.logger.error(`Failed to get key ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除键 
   */
  async del(key: string | string[], dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const keys = Array.isArray(key) ? key : [key];
      const fullKeys = keys.map(k => this.buildDataTypeKey(k, dataType, serverId));
      return await this.redis.del(...fullKeys);
    } catch (error) {
      this.logger.error(`Failed to delete key(s): ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查键是否存在 
   */
  async exists(key: string, dataType?: DataType, serverId?: string): Promise<boolean> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return (await this.redis.exists(fullKey)) === 1;
    } catch (error) {
      this.logger.error(`Failed to check existence of key ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 设置过期时间 
   */
  async expire(key: string, seconds: number, dataType?: DataType, serverId?: string): Promise<boolean> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return (await this.redis.expire(fullKey, seconds)) === 1;
    } catch (error) {
      this.logger.error(`Failed to set expiration for key ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取剩余过期时间 
   */
  async ttl(key: string, dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.ttl(fullKey);
    } catch (error) {
      this.logger.error(`Failed to get TTL for key ${key}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 哈希操作 ====================

  /**
   * 设置哈希字段 
   */
  async hset(key: string, field: string, value: any, dataType?: DataType, serverId?: string): Promise<void> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      await this.redis.hset(fullKey, field, this.serialize(value));
    } catch (error) {
      this.logger.error(`Failed to hset ${key}.${field}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取哈希字段 
   */
  async hget<T>(key: string, field: string, dataType?: DataType, serverId?: string): Promise<T | null> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const value = await this.redis.hget(fullKey, field);
      return value ? this.deserialize<T>(value) : null;
    } catch (error) {
      this.logger.error(`Failed to hget ${key}.${field}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取所有哈希字段 
   */
  async hgetall<T>(key: string, dataType?: DataType, serverId?: string): Promise<Record<string, T>> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const hash = await this.redis.hgetall(fullKey);
      const result: Record<string, T> = {};

      for (const [field, value] of Object.entries(hash)) {
        result[field] = this.deserialize<T>(value);
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to hgetall ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除哈希字段 
   */
  async hdel(key: string, fields: string[], dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.hdel(fullKey, ...fields);
    } catch (error) {
      this.logger.error(`Failed to hdel ${key}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 列表操作====================



  /**
   * 从右侧弹出列表 
   */
  async rpop<T>(key: string, dataType?: DataType, serverId?: string): Promise<T | null> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const value = await this.redis.rpop(fullKey);
      return value ? this.deserialize<T>(value) : null;
    } catch (error) {
      this.logger.error(`Failed to rpop from ${key}: ${error.message}`);
      throw error;
    }
  }


  /**
   * 获取列表长度
   */
  async llen(key: string, dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.llen(fullKey);
    } catch (error) {
      this.logger.error(`Failed to llen ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取列表范围内的元素
   */
  async lrange<T = string>(key: string, start: number, stop: number, dataType?: DataType, serverId?: string): Promise<T[]> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const result = await this.redis.lrange(fullKey, start, stop);

      // 如果T是string类型，直接返回
      if (typeof result[0] === 'string' && !result[0]?.startsWith('{') && !result[0]?.startsWith('[')) {
        return result as T[];
      }

      // 否则尝试反序列化
      return result.map(item => {
        try {
          return this.deserialize<T>(item);
        } catch {
          return item as T;
        }
      });
    } catch (error) {
      this.logger.error(`Failed to lrange ${key}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 有序集合操作 ====================

  /**
   * 添加到有序集合 
   */
  async zadd(key: string, score: number, member: string, dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.zadd(fullKey, score, member);
    } catch (error) {
      this.logger.error(`Failed to zadd to ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取有序集合范围（按分数升序）
   */
  async zrange(key: string, start: number, stop: number, withScoresOrOptions?: boolean | string, dataType?: DataType, serverId?: string): Promise<string[] | Array<{ member: string; score: number }>> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);

      if (withScoresOrOptions === true || withScoresOrOptions === 'WITHSCORES') {
        const result = await this.redis.zrange(fullKey, start, stop, 'WITHSCORES');
        const pairs: Array<{ member: string; score: number }> = [];
        for (let i = 0; i < result.length; i += 2) {
          pairs.push({
            member: result[i],
            score: parseFloat(result[i + 1]),
          });
        }
        return pairs;
      } else {
        return await this.redis.zrange(fullKey, start, stop);
      }
    } catch (error) {
      this.logger.error(`Failed to zrange ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取有序集合范围（按分数降序）
   */
  async zrevrange(key: string, start: number, stop: number, withScores = false, dataType?: DataType, serverId?: string): Promise<string[] | Array<{ member: string; score: number }>> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);

      if (withScores) {
        const result = await this.redis.zrevrange(fullKey, start, stop, 'WITHSCORES');
        const pairs: Array<{ member: string; score: number }> = [];
        for (let i = 0; i < result.length; i += 2) {
          pairs.push({
            member: result[i],
            score: parseFloat(result[i + 1]),
          });
        }
        return pairs;
      } else {
        return await this.redis.zrevrange(fullKey, start, stop);
      }
    } catch (error) {
      this.logger.error(`Failed to zrevrange ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取成员分数 
   */
  async zscore(key: string, member: string, dataType?: DataType, serverId?: string): Promise<number | null> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const score = await this.redis.zscore(fullKey, member);
      return score !== null ? parseFloat(score) : null;
    } catch (error) {
      this.logger.error(`Failed to zscore ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除有序集合成员
   */
  async zrem(key: string, members: string | string[], dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const memberArray = Array.isArray(members) ? members : [members];
      return await this.redis.zrem(fullKey, ...memberArray);
    } catch (error) {
      this.logger.error(`Failed to zrem from ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 弹出有序集合中分数最小的成员
   */
  async zpopmin(key: string, count: number = 1, dataType?: DataType, serverId?: string): Promise<string[]> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.zpopmin(fullKey, count);
    } catch (error) {
      this.logger.error(`Failed to zpopmin from ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取有序集合成员数量
   */
  async zcard(key: string, dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.zcard(fullKey);
    } catch (error) {
      this.logger.error(`Failed to zcard ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据分数范围获取成员
   */
  async zrangebyscore(key: string, min: number, max: number, dataType?: DataType, serverId?: string): Promise<string[]> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.zrangebyscore(fullKey, min, max);
    } catch (error) {
      this.logger.error(`Failed to zrangebyscore ${key}: ${error.message}`);
      throw error;
    }
  }


  // ==================== 列表操作 ====================

  /**
   * 从列表左侧推入元素
   */
  async lpush(key: string, values: any | any[], dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const valueArray = Array.isArray(values) ? values : [values];
      const serializedValues = valueArray.map(value =>
        typeof value === 'string' ? value : this.serialize(value)
      );
      return await this.redis.lpush(fullKey, ...serializedValues);
    } catch (error) {
      this.logger.error(`Failed to lpush to ${key}: ${error.message}`);
      throw error;
    }
  }




  /**
   * 移除列表中的元素
   */
  async lrem(key: string, count: number, value: string, dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.lrem(fullKey, count, value);
    } catch (error) {
      this.logger.error(`Failed to lrem from ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 设置列表指定位置的值
   */
  async lset(key: string, index: number, value: string, dataType?: DataType, serverId?: string): Promise<void> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      await this.redis.lset(fullKey, index, value);
    } catch (error) {
      this.logger.error(`Failed to lset ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取列表指定位置的元素
   */
  async lindex(key: string, index: number, dataType?: DataType, serverId?: string): Promise<string | null> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      return await this.redis.lindex(fullKey, index);
    } catch (error) {
      this.logger.error(`Failed to lindex ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 修剪列表，只保留指定范围内的元素
   */
  async ltrim(key: string, start: number, stop: number, dataType?: DataType, serverId?: string): Promise<void> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      await this.redis.ltrim(fullKey, start, stop);
    } catch (error) {
      this.logger.error(`Failed to ltrim ${key}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 集合操作 ====================

  /**
   * 向集合添加成员
   */
  async sadd(key: string, members: string | string[], dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const memberArray = Array.isArray(members) ? members : [members];
      return await this.redis.sadd(fullKey, ...memberArray);
    } catch (error) {
      this.logger.error(`Failed to sadd to ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从集合移除成员
   */
  async srem(key: string, members: string | string[], dataType?: DataType, serverId?: string): Promise<number> {
    try {
      const fullKey = this.buildDataTypeKey(key, dataType, serverId);
      const memberArray = Array.isArray(members) ? members : [members];
      return await this.redis.srem(fullKey, ...memberArray);
    } catch (error) {
      this.logger.error(`Failed to srem from ${key}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 发布订阅 ====================

  /**
   * 发布消息到频道
   */
  async publish(channel: string, message: string): Promise<number> {
    try {
      return await this.redis.publish(channel, message);
    } catch (error) {
      this.logger.error(`Failed to publish to ${channel}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 序列化数据
   */
  private serialize(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    return JSON.stringify(value);
  }

  /**
   * 反序列化数据
   */
  private deserialize<T>(value: string): T {
    try {
      return JSON.parse(value);
    } catch {
      return value as unknown as T;
    }
  }

  /**
   * 构建键名
   */
  buildKey(pattern: string, params: Record<string, string | number>): string {
    let key = pattern;
    for (const [param, value] of Object.entries(params)) {
      key = key.replace(`{${param}}`, String(value));
    }
    return key;
  }

  /**
   * 获取匹配的键 - 返回所有数据类型的合集
   * 根据文档要求：keys方法应该返回global、cross和server的合集
   */
  async keys(pattern: string, dataType?: DataType, serverId?: string): Promise<string[]> {
    try {
      const keyPrefix = this.redisConfig?.keyPrefix || '';
      const basePrefix = keyPrefix.replace(/:$/, '');
      const serviceContext = this.getServiceContext();

      if (dataType) {
        // 如果指定了dataType，只搜索该类型
        const fullPattern = this.buildDataTypeKey(pattern, dataType, serverId);

        // 🔧 手动添加keyPrefix，因为redis.keys()不会自动添加
        const keyPrefix = this.redisConfig?.keyPrefix || '';
        const searchPattern = keyPrefix + fullPattern;

        // console.log(`[RedisService.keys] 搜索模式调试:`);
        // console.log(`  pattern: "${pattern}"`);
        // console.log(`  dataType: "${dataType}"`);
        // console.log(`  fullPattern: "${fullPattern}"`);
        // console.log(`  keyPrefix: "${keyPrefix}"`);
        // console.log(`  searchPattern: "${searchPattern}"`);

        const keys = await this.redis.keys(searchPattern);
        // console.log(`[RedisService.keys] 找到的Redis键: ${keys.length}个 - ${keys.join(', ')}`);

        const businessKeys = keys.map(key => this.extractBusinessKey(key));
        // console.log(`[RedisService.keys] 提取的业务键: ${businessKeys.length}个 - ${businessKeys.join(', ')}`);

        return businessKeys;
      } else {
        // 搜索所有数据类型的合集：global、cross、server
        const patterns = [
          `${basePrefix}:global:${pattern}`,                   // 🔥 优化：global去掉服务前缀
          `${basePrefix}:cross:${pattern}`,                    // 🔥 优化：cross去掉服务前缀
          `${basePrefix}:server*:${serviceContext}:${pattern}`, // 保持：server保留服务前缀
        ];

        const allKeys: string[] = [];
        for (const searchPattern of patterns) {
          const keys = await this.redis.keys(searchPattern);
          allKeys.push(...keys);
        }

        // 去重并返回业务键名
        const uniqueKeys = [...new Set(allKeys)];
        return uniqueKeys.map(key => this.extractBusinessKey(key));
      }
    } catch (error) {
      this.logger.error(`获取键列表失败，模式: ${pattern}`, error);
      throw error;
    }
  }

  /**
   * 从完整键名中提取业务键名
   */
  private extractBusinessKey(fullKey: string): string {
    // 移除前缀，只保留业务键名
    const parts = fullKey.split(':');

    if (parts.length >= 3) {
      const dataType = parts[2]; // 第三部分是数据类型

      if (dataType === 'global' || dataType === 'cross') {
        // 🔥 优化：global和cross类型去掉了服务前缀
        // 格式：{env}:{project}:{dataType}:{businessKey}
        return parts.slice(3).join(':');
      } else if (dataType.startsWith('server')) {
        // server类型保留服务前缀
        // 格式：{env}:{project}:server{id}:{service}:{businessKey}
        return parts.slice(4).join(':');
      }
    }

    return fullKey;
  }

  /**
   * 批量删除匹配的键（自动处理前缀）
   */
  async deletePattern(pattern: string, dataType?: DataType, serverId?: string): Promise<number> {
    try {
      // 使用封装的keys方法获取匹配的业务键名
      const businessKeys = await this.keys(pattern, dataType, serverId);
      if (businessKeys.length === 0) {
        return 0;
      }

      // 批量删除，每个键都需要传递dataType
      let deletedCount = 0;
      for (const key of businessKeys) {
        const result = await this.del(key, dataType, serverId);
        if (result > 0) deletedCount++;
      }

      return deletedCount;
    } catch (error) {
      this.logger.error(`批量删除键失败，模式: ${pattern}`, error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  async ping(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      this.logger.error(`Redis ping failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取 Redis 信息
   */
  async getInfo(): Promise<any> {
    try {
      const info = await this.redis.info();
      return this.parseRedisInfo(info);
    } catch (error) {
      this.logger.error(`Failed to get Redis info: ${error.message}`);
      throw error;
    }
  }

  /**
   * 解析 Redis 信息
   */
  private parseRedisInfo(info: string): any {
    const lines = info.split('\r\n');
    const result: any = {};
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = isNaN(Number(value)) ? value : Number(value);
      }
    }
    
    return result;
  }

  /**
   * 获取原始 Redis 客户端
   */
  getClient(): Redis {
    if (!this.redis) {
      throw new Error('Redis client not initialized. Make sure RedisService.onModuleInit() has been called.');
    }
    if (this.healthStatus !== 'ready') {
      throw new Error('Redis client not ready. Current status: ' + this.healthStatus);
    }
    return this.redis;
  }

  /**
   * 获取订阅客户端
   */
  getSubscriber(): Redis {
    if (!this.subscriber) {
      throw new Error('Redis subscriber not initialized. Make sure RedisService.onModuleInit() has been called and Redis connection is ready.');
    }
    if (this.healthStatus !== 'ready') {
      throw new Error('Redis subscriber not ready. Current status: ' + this.healthStatus);
    }
    return this.subscriber;
  }

  /**
   * 获取默认 Redis 配置（当没有传入配置时使用）
   */
  private getDefaultRedisConfig(): any {
    const redisUrl = process.env.REDIS_URL;
    const redisHost = process.env.REDIS_HOST || 'localhost';
    const redisPort = parseInt(process.env.REDIS_PORT || '6379', 10);
    const redisPassword = process.env.REDIS_PASSWORD;
    const redisCluster = process.env.REDIS_CLUSTER === 'true';

    if (redisUrl) {
      return redisUrl;
    }

    if (redisCluster) {
      // 集群模式配置
      return {
        enableAutoPipelining: true,
        maxRedirections: 16,
        retryDelayOnClusterDown: 300,
        retryDelayOnFailover: 100,
        scaleReads: 'slave',
        redisOptions: {
          password: redisPassword,
        },
      };
    } else {
      // 单实例模式配置
      return {
        host: redisHost,
        port: redisPort,
        password: redisPassword,
        db: 0,
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        family: 4,
        connectTimeout: 10000,
        commandTimeout: 5000,
      };
    }
  }
}
