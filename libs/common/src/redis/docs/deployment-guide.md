# Redis 服务部署指南

## 概述

Redis 在足球经理游戏架构中作为独立的基础设施服务，承担多重关键职责。本指南详细说明了 Redis 服务的部署、配置和管理。

## 🚀 快速部署

### 1. 环境准备

```bash
# 检查 Docker 环境
docker --version
docker-compose --version

# 克隆项目
git clone <repository-url>
cd football-manager-server
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置
vim .env
```

```bash
# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-strong-password
REDIS_CLUSTER=false
REDIS_KEY_PREFIX=fm:

# 环境配置
NODE_ENV=development
```

### 3. 启动 Redis 服务

```bash
# 初始化 Redis 服务
./scripts/redis-setup.sh setup

# 启动单实例模式（开发环境）
./scripts/redis-setup.sh start single

# 启动主从模式（测试环境）
./scripts/redis-setup.sh start replica

# 启动集群模式（生产环境）
./scripts/redis-setup.sh start sentinel
```

### 4. 验证部署

```bash
# 检查服务状态
./scripts/redis-setup.sh status

# 连接 Redis CLI
./scripts/redis-setup.sh cli master

# 测试连接
redis> ping
PONG
```

## 🏗️ 部署架构

### 开发环境 - 单实例模式

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data 
```

**特点**:
- 简单快速，适合本地开发
- 1GB 内存配置
- 基础持久化设置

### 测试环境 - 主从复制模式

```yaml
# docker-compose.redis.yml
services:
  redis-master:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    
  redis-slave:
    image: redis:7.2-alpine
    ports:
      - "6380:6379"
    command: redis-server --replicaof redis-master 6379
    depends_on:
      - redis-master
```

**特点**:
- 数据安全保障
- 读写分离支持
- 2GB 内存配置
- 自动故障检测

### 生产环境 - 集群 + Sentinel 模式

```yaml
# 完整高可用配置
services:
  redis-master:
    # 主节点配置
  redis-slave:
    # 从节点配置
  redis-sentinel-1:
    # Sentinel 监控节点1
  redis-sentinel-2:
    # Sentinel 监控节点2
  redis-sentinel-3:
    # Sentinel 监控节点3
```

**特点**:
- 高可用性保障
- 自动故障转移
- 8GB+ 内存配置
- TLS 加密传输

## ⚙️ 配置优化

### Redis 配置文件

```bash
# config/redis/redis.conf
# 网络配置
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 内存管理
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 持久化配置
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# 安全配置
requirepass your-strong-password
rename-command FLUSHDB ""
rename-command FLUSHALL ""
```

### Sentinel 配置

```bash
# config/redis/sentinel.conf
port 26379
sentinel monitoring mymaster redis-master 6379 2
sentinel auth-pass mymaster your-password
sentinel down-after-milliseconds mymaster 30000
sentinel failover-timeout mymaster 180000
```

## 📊 监控和运维

### 健康检查

```bash
# 检查 Redis 状态
./scripts/redis-setup.sh status

# 查看 Redis 信息
redis-cli -a password info

# 检查内存使用
redis-cli -a password info memory

# 查看连接数
redis-cli -a password info clients
```

### 性能监控

```bash
# 查看慢查询
redis-cli -a password slowlog get 10

# 监控实时命令
redis-cli -a password monitoring

# 查看键空间统计
redis-cli -a password info keyspace
```

### 数据备份

```bash
# 创建备份
./scripts/redis-setup.sh backup

# 恢复数据
./scripts/redis-setup.sh restore backup-file.rdb

# 自动备份脚本
0 2 * * * /path/to/redis-setup.sh backup
```

## 🔒 安全配置

### 访问控制

```bash
# 设置密码
CONFIG SET requirepass your-strong-password

# 创建用户（Redis 6.0+）
ACL SETUSER app_user on >app_password ~* &* +@all -flushdb -flushall
ACL SETUSER readonly_user on >readonly_password ~* &* +@read
```

### 网络安全

```bash
# 绑定特定IP
bind 127.0.0.1 **********

# 启用保护模式
protected-mode yes

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG "CONFIG_SECRET"
```

### TLS 加密

```bash
# 生成证书
openssl req -x509 -newkey rsa:4096 -keyout redis.key -out redis.crt -days 365

# 配置 TLS
tls-port 6380
tls-cert-file /etc/redis/tls/redis.crt
tls-key-file /etc/redis/tls/redis.key
```

## 🚨 故障排除

### 常见问题

**连接失败**:
```bash
# 检查容器状态
docker ps | grep redis

# 检查端口
netstat -tlnp | grep 6379

# 查看日志
docker logs redis-container
```

**内存不足**:
```bash
# 查看内存使用
redis-cli info memory

# 清理过期键
redis-cli --scan --pattern "*" | xargs redis-cli del

# 调整内存策略
CONFIG SET maxmemory-policy allkeys-lru
```

**主从同步问题**:
```bash
# 检查复制状态
redis-cli info replication

# 重新同步
SLAVEOF redis-master 6379
```

### 性能调优

**慢查询优化**:
```bash
# 查看慢查询
SLOWLOG GET 10

# 设置慢查询阈值
CONFIG SET slowlog-log-slower-than 10000
```

**连接数优化**:
```bash
# 查看连接数
INFO clients

# 设置最大连接数
CONFIG SET maxclients 10000
```

## 📈 扩展部署

### 水平扩展

```bash
# 添加从节点
docker-compose scale redis-slave=3

# 添加 Sentinel 节点
docker-compose scale redis-sentinel=5
```

### 集群扩展

```bash
# 创建 Redis 集群
redis-cli --cluster create \
  192.168.1.100:7001 \
  192.168.1.100:7002 \
  192.168.1.100:7003 \
  --cluster-replicas 1
```

### Kubernetes 部署

```yaml
# redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
      role: master
  template:
    metadata:
      labels:
        app: redis
        role: master
    spec:
      containers:
      - name: redis
        image: redis:7.2-alpine
        ports:
        - containerPort: 6379
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
```

## 🎯 最佳实践

1. **资源规划**: 根据业务需求合理分配内存和CPU
2. **数据分区**: 使用不同数据库分离不同类型的数据
3. **监控告警**: 设置完善的监控和告警机制
4. **备份策略**: 定期备份，测试恢复流程
5. **安全加固**: 启用认证、网络隔离、命令重命名
6. **性能优化**: 合理设置过期策略、内存策略
7. **高可用**: 生产环境使用主从复制和 Sentinel
8. **文档维护**: 保持部署文档和运维手册更新

通过遵循这些部署指南和最佳实践，可以确保 Redis 服务在足球经理游戏中稳定、高效地运行。
