/**
 * 🚀 微服务工具函数（精简版）
 *
 * 新架构说明：
 * - 微服务启动功能已转移到ServiceMesh
 * - 这里只保留通用的优雅关闭功能
 */

/**
 * 优雅关闭微服务
 */
export async function gracefulShutdown(app: any) {
  console.log('🛑 正在优雅关闭微服务...');
  
  // 设置关闭超时
  const shutdownTimeout = setTimeout(() => {
    console.error('❌ 关闭超时，强制退出');
    process.exit(1);
  }, 10000);

  try {
    await app.close();
    clearTimeout(shutdownTimeout);
    console.log('✅ 微服务已优雅关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中发生错误:', error);
    clearTimeout(shutdownTimeout);
    process.exit(1);
  }
}

/**
 * 注册优雅关闭信号处理
 */
export function setupGracefulShutdown(app: any) {
  process.on('SIGTERM', () => gracefulShutdown(app));
  process.on('SIGINT', () => gracefulShutdown(app));
  process.on('SIGUSR2', () => gracefulShutdown(app)); // nodemon 重启信号
}
