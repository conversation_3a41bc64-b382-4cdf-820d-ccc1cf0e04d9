import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MicroserviceClientModule } from './microservice-client.module';
import { MicroserviceName } from '@shared/constants';
import microserviceKitConfig from './config/default.config';

/**
 * 🚀 微服务公共库统一模块入口（精简版）
 * 专注微服务调用功能，服务注册功能已转移到ServiceMesh
 *
 * 新架构职责：
 * - MicroserviceKit: 纯微服务调用库
 * - ServiceMesh: 服务注册、发现、治理
 */
@Module({})
export class MicroserviceKitModule {

  /**
   * 🚀 纯客户端模式（支持分区分服架构）
   *
   * 新架构中的唯一方法，专注微服务调用功能：
   * - 支持区服感知调用
   * - 支持服务白名单验证
   * - 支持连接池管理
   * - 支持负载均衡
   *
   * @param options 客户端选项
   * @param options.services 要连接的服务列表，不指定则连接所有服务
   * @param options.enableServerAware 是否启用区服感知功能，默认true
   * @param options.enableTraditionalFallback 是否启用传统调用降级，默认false（新架构中禁用）
   */
  static forClient(options?: {
    services?: MicroserviceName[];
    enableServerAware?: boolean;
    enableTraditionalFallback?: boolean;
  }): DynamicModule {
    const imports = [
      ConfigModule.forFeature(microserviceKitConfig),
      MicroserviceClientModule.forRoot({
        services: options?.services,
        enableServerAware: options?.enableServerAware ?? true, // 🚀 默认启用区服感知
        enableTraditionalFallback: options?.enableTraditionalFallback ?? false, // 🚀 默认禁用降级
      }),
    ];

    return {
      module: MicroserviceKitModule,
      imports,
      exports: [MicroserviceClientModule],
    };
  }

  // 🗑️ forServer() 和 forHybrid() 方法已删除
  // 服务注册功能已转移到 ServiceMeshModule
  // 新架构中只保留纯调用功能
}
