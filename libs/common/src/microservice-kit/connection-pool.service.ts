import { Injectable, Logger, OnModule<PERSON><PERSON>roy, Inject } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import { MicroserviceKitConfig } from './config/microservice.config';

/**
 * 连接池服务
 * 管理微服务实例的连接复用，提升性能
 */
@Injectable()
export class ConnectionPoolService implements OnModuleDestroy {
  private readonly logger = new Logger(ConnectionPoolService.name);

  constructor(
    @Inject('MICROSERVICE_CONFIG') private readonly config: MicroserviceKitConfig,
  ) {}
  
  // 连接池：instanceKey -> ClientProxy
  private readonly connections = new Map<string, ClientProxy>();
  
  // 连接使用统计
  private readonly stats = new Map<string, { 
    uses: number; 
    errors: number; 
    lastUsed: Date;
    created: Date;
  }>();

  /**
   * 获取连接（复用现有连接或创建新连接）
   */
  async getConnection(instance: any): Promise<ClientProxy> {
    const instanceKey = `${instance.host}:${instance.port}`;
    
    // 尝试复用现有连接
    let client = this.connections.get(instanceKey);
    
    if (!client || !this.isConnectionHealthy(client)) {
      // 创建新连接
      client = await this.createConnection(instance);
      this.connections.set(instanceKey, client);
      this.stats.set(instanceKey, { 
        uses: 0, 
        errors: 0, 
        lastUsed: new Date(),
        created: new Date()
      });
      
      this.logger.debug(`创建新连接: ${instanceKey}`);
    }
    
    // 更新使用统计
    const stat = this.stats.get(instanceKey);
    if (stat) {
      stat.uses++;
      stat.lastUsed = new Date();
    }
    
    return client;
  }

  /**
   * 创建连接（支持动态端口）
   */
  private async createConnection(instance: any): Promise<ClientProxy> {
    try {
      // 获取服务配置以使用正确的传输方式
      const serviceConfig = this.config.services[instance.serviceName];
      if (!serviceConfig) {
        throw new Error(`服务配置未找到: ${instance.serviceName}`);
      }

      // ✅ 支持动态端口：根据传输类型动态构建选项
      const dynamicOptions = {
        ...serviceConfig.options,
        // 对于TCP传输，使用实例的动态端口和主机
        ...(serviceConfig.transport === Transport.TCP && {
          host: instance.host,
          port: instance.port,
        }),
        // 对于Redis传输，保持原有配置（不受端口影响）
      };

      this.logger.debug(`🔧 创建动态连接: ${instance.serviceName} -> ${instance.host}:${instance.port}, transport: ${serviceConfig.transport}`);

      const client = ClientProxyFactory.create({
        transport: serviceConfig.transport,
        options: dynamicOptions,
      });

      // 预连接以验证可用性
      await client.connect();

      this.logger.debug(`✅ 连接创建成功: ${instance.host}:${instance.port}`);
      return client;
    } catch (error) {
      this.logger.error(`❌ 连接创建失败: ${instance.host}:${instance.port}`, error);
      throw error;
    }
  }

  /**
   * 检查连接健康状态
   */
  private isConnectionHealthy(client: ClientProxy): boolean {
    // 简单实现：假设连接总是健康的
    // 实际实现可以发送ping消息检查
    try {
      // 检查客户端是否已关闭
      return client && typeof client.send === 'function';
    } catch {
      return false;
    }
  }

  /**
   * 移除连接
   */
  async removeConnection(instanceKey: string): Promise<void> {
    const client = this.connections.get(instanceKey);
    if (client) {
      try {
        await client.close();
        this.connections.delete(instanceKey);
        this.stats.delete(instanceKey);
        this.logger.debug(`连接已移除: ${instanceKey}`);
      } catch (error) {
        this.logger.error(`关闭连接失败: ${instanceKey}`, error);
      }
    }
  }

  /**
   * 更新连接错误统计
   */
  updateErrorStats(instanceKey: string): void {
    const stat = this.stats.get(instanceKey);
    if (stat) {
      stat.errors++;
    }
  }

  /**
   * 获取连接池统计
   */
  getStats(): Map<string, any> {
    const result = new Map();
    for (const [key, stat] of this.stats) {
      result.set(key, {
        ...stat,
        hasConnection: this.connections.has(key),
        uptime: Date.now() - stat.created.getTime(),
        idleTime: Date.now() - stat.lastUsed.getTime(),
      });
    }
    return result;
  }

  /**
   * 获取连接池大小
   */
  getPoolSize(): number {
    return this.connections.size;
  }

  /**
   * 清理空闲连接
   */
  async cleanupIdleConnections(maxIdleTime: number = 300000): Promise<void> {
    const now = Date.now();
    const toRemove: string[] = [];

    for (const [key, stat] of this.stats) {
      const idleTime = now - stat.lastUsed.getTime();
      if (idleTime > maxIdleTime) {
        toRemove.push(key);
      }
    }

    for (const key of toRemove) {
      await this.removeConnection(key);
      this.logger.debug(`清理空闲连接: ${key}`);
    }

    if (toRemove.length > 0) {
      this.logger.log(`清理了 ${toRemove.length} 个空闲连接`);
    }
  }

  /**
   * 模块销毁时清理所有连接
   */
  async onModuleDestroy(): Promise<void> {
    this.logger.log('正在清理连接池...');
    
    const closePromises: Promise<void>[] = [];
    
    for (const [key, client] of this.connections) {
      closePromises.push(
        client.close().catch(error => {
          this.logger.error(`关闭连接失败: ${key}`, error);
        })
      );
    }
    
    await Promise.all(closePromises);
    
    this.connections.clear();
    this.stats.clear();
    
    this.logger.log(`连接池清理完成，共清理 ${closePromises.length} 个连接`);
  }
}
