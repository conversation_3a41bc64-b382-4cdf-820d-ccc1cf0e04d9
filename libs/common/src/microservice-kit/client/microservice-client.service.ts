import { Injectable, Inject, Optional, Logger } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import { ModuleRef } from '@nestjs/core';
import { timeout } from 'rxjs/operators';
import { MicroserviceKitConfig } from '../config/microservice.config';
import { MicroserviceName } from '@shared/constants';

// 导入ServiceMesh组件
import {
  ServerAwareRegistryService,
  GlobalServiceRegistryService,
  ServerAwareLoadBalancerService
} from '@libs/service-mesh';
// 导入连接池服务
import { ConnectionPoolService } from './connection-pool.service';
// 导入工具服务
import { ContextExtractorService } from '../utils/context-extractor.service';
import { PortManager } from '@common/port-manager';

// 服务实例类型（临时定义，避免循环依赖）
interface ServiceInstance {
  instanceName: string;
  host: string;
  port: number;
  serviceName: string;
  serverId: string;
}

/**
 * 微服务客户端服务
 * 提供统一的微服务调用接口
 */
@Injectable()
export class MicroserviceClientService {
  private readonly logger = new Logger(MicroserviceClientService.name);
  private clients = new Map<MicroserviceName, ClientProxy>();
  private connectedServices: MicroserviceName[];
  private clientOptions: any;

  constructor(
      @Inject('MICROSERVICE_CONFIG') private config: MicroserviceKitConfig,
      @Inject('CONNECTED_SERVICES') connectedServices: MicroserviceName[],
      // 🚀 新增：客户端选项配置
      @Inject('MICROSERVICE_CLIENT_OPTIONS') clientOptions: any,
      // 🚀 ServiceMesh组件注入
      @Optional() private serviceRegistry?: ServerAwareRegistryService,
      @Optional() private globalRegistry?: GlobalServiceRegistryService,
      @Optional() private serverAwareLoadBalancer?: ServerAwareLoadBalancerService,
      // 新增：可选注入连接池服务
      @Optional() private connectionPool?: ConnectionPoolService,
      // 新增：可选注入工具服务
      @Optional() private contextExtractor?: ContextExtractorService,
  ) {
    this.connectedServices = connectedServices;
    this.clientOptions = clientOptions;
    this.initializeClients();
  }

  /**
   * 初始化客户端连接
   */
  private initializeClients() {
    // 只初始化已连接的服务
    this.connectedServices.forEach(serviceName => {
      const service = this.config.services[serviceName];
      if (!service) {
        console.warn(`⚠️ 服务配置未找到: ${serviceName}`);
        return;
      }

      try {
        // 使用 ClientProxyFactory 直接创建客户端
        const client = ClientProxyFactory.create({
          transport: service.transport,
          options: service.options,
        });

        this.clients.set(serviceName, client);
        console.log(`✅ 微服务客户端已创建: ${serviceName} -> ${service.name}`);
        console.log(`🔗 连接配置: ${JSON.stringify(service.options)}`);
      } catch (error) {
        console.error(`❌ 微服务客户端创建失败: ${serviceName}`, error);
      }
    });
  }

  /**
   * 获取指定服务的客户端
   */
  getClient(serviceName: MicroserviceName): ClientProxy {
    const client = this.clients.get(serviceName);
    if (!client) {
      const availableServices = this.getAvailableServices().join(', ');
      throw new Error(
          `微服务客户端未找到: ${serviceName}。` +
          `可用服务: [${availableServices}]。` +
          `请检查服务是否已在模块中注册。`
      );
    }
    return client;
  }

  /**
   * 🚀 统一调用方法（智能路由版 - 区分全局服务和区服服务）
   * 新架构：根据服务类型智能选择调用方式
   */
  async call<T = any>(serviceName: MicroserviceName, pattern: string, data?: any): Promise<T> {
    // 🎯 检查服务白名单（如果配置了的话）
    if (!this.isServiceAllowed(serviceName)) {
      throw new Error(`🚨 服务 ${serviceName} 不在允许的服务白名单中`);
    }

    // 🧠 智能判断服务类型
    const isGlobalService = this.isGlobalService(serviceName);

    if (isGlobalService) {
      // 🌍 全局服务调用
      this.logger.log(`🌍 全局服务调用: ${serviceName}.${pattern}`);
      return this.callGlobalService(serviceName, pattern, data);
    } else {
      // 🏰 区服服务调用
      const serverId = this.contextExtractor?.extractServerId(data) || this.extractServerId(data);
      if (!serverId) {
        const defaultServerId = this.getDefaultServerId();
        this.logger.log(`🎯 未提供区服ID，使用默认区服: ${defaultServerId} for ${serviceName}.${pattern}`);
        return this.callServerAware(serviceName, defaultServerId, pattern, data);
      }
      return this.callServerAware(serviceName, serverId, pattern, data);
    }
  }

  /**
   * 🌍 全局服务调用
   */
  private async callGlobalService<T>(
    serviceName: MicroserviceName,
    pattern: string,
    data?: any
  ): Promise<T> {
    // 🌍 使用全局服务注册中心查找实例
    const instance = await this.selectGlobalServiceInstance(serviceName);

    if (!instance) {
      const error = new Error(`🚨 未找到健康的全局服务实例: ${serviceName}`);
      this.logger.error(error.message);
      throw error;
    }

    // 调用服务实例
    return this.callInstance(instance, pattern, data);
  }

  /**
   * 🧠 判断是否为全局服务
   */
  private isGlobalService(serviceName: MicroserviceName): boolean {
    // 🧠 根据服务名称判断服务类型
    // Auth服务是全局服务，其他服务是区服服务
    const globalServices = ['auth', 'notification'];
    return globalServices.includes(serviceName);
  }

  /**
   * 🌍 选择全局服务实例（使用ServiceMesh负载均衡）
   */
  private async selectGlobalServiceInstance(serviceName: MicroserviceName): Promise<ServiceInstance | null> {
    try {
      // 🚀 优先使用ServiceMesh的全局服务注册中心和负载均衡
      if (this.globalRegistry) {
        this.logger.debug(`🌍 使用ServiceMesh全局负载均衡选择实例: ${serviceName}`);
        const instance = await this.globalRegistry.selectInstance(serviceName, 'round-robin');
        this.logger.debug(`🌍 ServiceMesh选择全局服务实例: ${instance.id} (${instance.host}:${instance.port})`);

        // 转换为标准ServiceInstance格式
        return {
          instanceName: instance.id,
          serviceName: instance.serviceName,
          host: instance.host,
          port: instance.port,
          serverId: 'global',
        };
      }

      // 🔧 Fallback：使用配置中的默认实例（当ServiceMesh不可用时）
      this.logger.warn(`⚠️ ServiceMesh全局注册中心不可用，使用fallback逻辑: ${serviceName}`);
      const serviceConfig = this.config.services[serviceName];
      if (!serviceConfig) {
        this.logger.error(`服务配置未找到: ${serviceName}`);
        return null;
      }

      const instance: ServiceInstance = {
        instanceName: `${serviceName}-fallback`,
        serviceName,
        host: 'localhost', // 🔧 全局服务使用localhost
        port: this.getServicePort(serviceName, 'global', 0), // ✅ 支持动态端口计算
        serverId: 'global',
      };

      this.logger.debug(`🌍 Fallback选择全局服务实例: ${instance.instanceName} (${instance.host}:${instance.port})`);
      return instance;
    } catch (error) {
      this.logger.error(`选择全局服务实例失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 🔧 获取服务端口（支持动态端口）
   */
  private getServicePort(serviceName: MicroserviceName, serverId?: string, instanceId: number = 0): number {
    try {
      // ✅ 使用PortManager计算动态端口
      return PortManager.calculatePort(serviceName, serverId, instanceId);
    } catch (error) {
      // ✅ 降级到传统端口配置
      this.logger.warn(`⚠️ 动态端口计算失败，使用固定端口配置: ${error.message}`);

      const serviceNameUpper = serviceName.toUpperCase();
      const envPort = process.env[`${serviceNameUpper}_PORT`] ||
                     process.env.PORT;

      if (envPort) {
        const port = parseInt(envPort.toString());
        if (!isNaN(port) && port > 0) {
          return port;
        }
      }

      return this.getDefaultPortByService(serviceName);
    }
  }

  /**
   * 获取服务的默认端口
   */
  private getDefaultPortByService(serviceName: string): number {
    const defaultPorts: Record<string, number> = {
      'auth': 3001,
      'character': 3002,
      'hero': 3003,
      'economy': 3004,
      'activity': 3005,
      'match': 3006,
      'guild': 3007,
      'social': 3008,
      'notification': 3009,
      'gateway': 3000,
    };
    return defaultPorts[serviceName] || 3000;
  }

  /**
   * 🚀 区服感知调用（使用ServiceMesh负载均衡）
   * 新架构：优先使用ServiceMesh负载均衡，fallback到简单选择
   */
  private async callServerAware<T>(
    serviceName: MicroserviceName,
    serverId: string,
    pattern: string,
    data?: any
  ): Promise<T> {
    this.logger.log(`🎯 区服感知调用: ${serviceName}.${pattern}@${serverId}`);

    let instance;

    // 🚀 优先使用ServiceMesh的区服感知注册中心（集成负载均衡）
    if (this.serviceRegistry) {
      this.logger.debug(`🎯 使用ServiceMesh区服感知负载均衡: ${serviceName}@${serverId}`);
      instance = await this.serviceRegistry.selectInstance(serviceName, serverId, 'round-robin');

      if (instance) {
        this.logger.debug(`🎯 ServiceMesh选择区服实例: ${instance.instanceName} (${instance.host}:${instance.port})`);

        // 转换为标准ServiceInstance格式
        const standardInstance: ServiceInstance = {
          instanceName: instance.instanceName,
          serviceName: instance.serviceName,
          host: instance.host,
          port: instance.port,
          serverId: instance.serverId,
        };

        return this.callInstance(standardInstance, pattern, data);
      }
    }

    // 🔧 Fallback：使用简单实例选择（当ServiceMesh不可用时）
    this.logger.warn(`⚠️ ServiceMesh负载均衡器不可用，使用fallback逻辑: ${serviceName}@${serverId}`);
    instance = await this.fallbackSelectInstance(serviceName, serverId);

    if (!instance) {
      const error = new Error(`🚨 未找到健康的服务实例: ${serviceName}@${serverId}`);
      this.logger.error(error.message);
      throw error;
    }

    return this.callInstance(instance, pattern, data);
  }

  /**
   * 发送事件到微服务
   */
  emit(serviceName: MicroserviceName, pattern: string, data?: any): void {
    const client = this.getClient(serviceName);
    client.emit(pattern, data);
    console.log(`📤 事件已发送: ${serviceName}.${pattern}`);
  }

  /**
   * 获取所有可用的服务列表
   */
  getAvailableServices(): MicroserviceName[] {
    return Array.from(this.clients.keys());
  }

  /**
   * 检查服务是否可用
   */
  isServiceAvailable(serviceName: MicroserviceName): boolean {
    return this.clients.has(serviceName);
  }

  /**
   * 调用特定的服务实例（优化版本，支持连接池）
   * 用于区服感知的路由
   */
  async callInstance<T = any>(instance: ServiceInstance, pattern: string, data?: any): Promise<T> {
    try {
      let client: ClientProxy;
      let shouldCloseClient = false;

      // 使用连接池（如果可用）
      if (this.connectionPool) {
        client = await this.connectionPool.getConnection(instance);
      } else {
        // 降级到原有逻辑：创建临时客户端，使用与服务相同的传输配置
        const serviceConfig = this.config.services[instance.serviceName];
        if (!serviceConfig) {
          throw new Error(`服务配置未找到: ${instance.serviceName}`);
        }

        client = ClientProxyFactory.create({
          transport: serviceConfig.transport,
          options: serviceConfig.options,
        });
        shouldCloseClient = true;
      }

      // 设置超时时间
      const timeoutMs = instance.serviceName === 'auth' ? 3000 : 5000;

      const result = await client.send<T>(pattern, data)
        .pipe(timeout(timeoutMs))
        .toPromise();

      this.logger.log(`📡 实例调用成功: ${instance.instanceName}.${pattern}`);

      // 如果是临时客户端，需要关闭
      if (shouldCloseClient) {
        await client.close();
      }

      return result;
    } catch (error) {
      this.logger.error(`❌ 实例调用失败: ${instance.instanceName}.${pattern}`, error);

      // 更新连接池错误统计
      if (this.connectionPool) {
        const instanceKey = `${instance.host}:${instance.port}`;
        this.connectionPool.updateErrorStats(instanceKey);
      }

      throw error;
    }
  }

  /**
   * 批量调用多个服务
   */
  async callMultiple<T = any>(
      calls: Array<{ serviceName: MicroserviceName; pattern: string; data?: any }>
  ): Promise<T[]> {
    const promises = calls.map(call =>
        this.call<T>(call.serviceName, call.pattern, call.data)
    );
    return Promise.all(promises);
  }

  /**
   * 从调用数据中提取区服ID
   */
  private extractServerId(data?: any): string | null {
    if (!data) return this.getDefaultServerId();

    // 1. 直接从数据中提取
    if (data.serverId) return data.serverId;

    // 2. 从服务器上下文中提取
    if (data.serverContext?.serverId) return data.serverContext.serverId;

    // 3. 从用户ID推断（可扩展）
    if (data.userId || data.characterId) {
      return this.inferServerIdFromUser(data.userId || data.characterId);
    }

    // 4. 返回默认区服ID
    return this.getDefaultServerId();
  }

  /**
   * 从用户ID推断区服ID（可根据业务逻辑实现）
   */
  private inferServerIdFromUser(userId: string): string | null {
    // 简单示例：可以查询缓存或数据库获取用户所在区服
    // 这里返回null，让业务代码显式传递serverId
    return null;
  }



  /**
   * 降级选择实例（当负载均衡器不可用时）
   */
  private async fallbackSelectInstance(serviceName: MicroserviceName, serverId: string): Promise<any | null> {
    if (!this.serviceRegistry) {
      return null;
    }

    try {
      const instances = await this.serviceRegistry.getHealthyInstances(serviceName, serverId);
      if (!instances || instances.length === 0) {
        return null;
      }

      // 简单选择第一个实例
      return instances[0];
    } catch (error) {
      this.logger.error(`降级选择实例失败: ${serviceName}@${serverId}`, error);
      return null;
    }
  }

  /**
   * 检查是否启用区服感知功能
   */
  private isServerAwareEnabled(): boolean {
    return this.clientOptions?.enableServerAware ?? true;
  }

  /**
   * 🚀 检查服务是否在白名单中
   */
  private isServiceAllowed(serviceName: MicroserviceName): boolean {
    // 如果没有配置白名单，则允许所有已连接的服务
    return this.connectedServices.includes(serviceName);
  }

  /**
   * 🚀 检查是否启用传统调用降级
   */
  private isTraditionalFallbackEnabled(): boolean {
    return this.clientOptions?.enableTraditionalFallback ?? false;
  }

  /**
   * 🚀 获取默认区服ID
   */
  private getDefaultServerId(): string {
    return process.env.SERVER_ID || 'server_001';
  }
}
