import * as net from 'net';

/**
 * 端口管理器
 * 
 * 核心功能：
 * 1. 算法化端口分配：通过数学公式确保端口唯一性
 * 2. 端口可用性检测：验证端口是否可以使用
 * 3. 配置统一管理：从环境变量读取基础端口配置
 * 4. 向后兼容支持：支持现有的固定端口配置
 * 
 * 核心算法：PORT = BASE_PORT + (SERVER_NUMBER * 10) + INSTANCE_ID
 * 
 * 示例：
 * - character服务基础端口：3200
 * - server_001实例0：3200 + (1 * 10) + 0 = 3210
 * - server_002实例1：3200 + (2 * 10) + 1 = 3221
 */
export class PortManager {
  
  /**
   * 默认基础端口配置
   * 优先级：环境变量 > 默认配置
   */
  private static readonly SERVICE_BASE_PORTS: Record<string, number> = {
    gateway: 3000,
    auth: 3100,
    character: 3200,
    hero: 3300,
    economy: 3400,
    social: 3500,
    activity: 3600,
    match: 3700,
  };

  /**
   * 计算服务的动态端口
   * 
   * @param serviceName 服务名称（如：'character', 'hero'）
   * @param serverId 区服ID（如：'server_001', 'server_002'）
   * @param instanceId 实例ID（默认：0）
   * @returns 计算出的端口号
   * 
   * @example
   * PortManager.calculatePort('character', 'server_001', 0) // 返回 3210
   * PortManager.calculatePort('hero', 'server_002', 1)      // 返回 3321
   */
  static calculatePort(serviceName: string, serverId?: string, instanceId: number = 0): number {
    // 1. 获取基础端口
    const basePort = this.getBasePort(serviceName);
    
    // 2. 提取区服编号
    const serverNumber = this.extractServerNumber(serverId);
    
    // 3. 验证实例ID范围
    if (instanceId < 0 || instanceId > 9) {
      throw new Error(`实例ID ${instanceId} 超出有效范围 [0-9]`);
    }
    
    // 4. 计算最终端口
    const calculatedPort = basePort + (serverNumber * 10) + instanceId;
    
    // 5. 验证端口范围
    if (calculatedPort < 1024 || calculatedPort > 65535) {
      throw new Error(`计算的端口 ${calculatedPort} 超出有效范围 [1024-65535]`);
    }
    
    return calculatedPort;
  }

  /**
   * 获取服务的基础端口
   * 
   * @param serviceName 服务名称
   * @returns 基础端口号
   * 
   * 优先级：
   * 1. 环境变量：{SERVICE_NAME}_BASE_PORT
   * 2. 默认配置：SERVICE_BASE_PORTS
   * 3. 抛出错误
   */
  static getBasePort(serviceName: string): number {
    // 1. 尝试从环境变量读取
    const envKey = `${serviceName.toUpperCase()}_BASE_PORT`;
    const envPort = process.env[envKey];
    if (envPort) {
      const port = parseInt(envPort.trim());
      if (!isNaN(port) && port > 0) {
        return port;
      }
    }
    
    // 2. 使用默认配置
    const defaultPort = this.SERVICE_BASE_PORTS[serviceName];
    if (defaultPort) {
      return defaultPort;
    }
    
    // 3. 抛出错误
    throw new Error(`未找到服务 ${serviceName} 的基础端口配置`);
  }

  /**
   * 从区服ID中提取数字编号
   * 
   * @param serverId 区服ID（如：'server_001', 'server_002'）
   * @returns 区服编号（如：1, 2）
   * 
   * @example
   * extractServerNumber('server_001') // 返回 1
   * extractServerNumber('server_123') // 返回 123
   * extractServerNumber('test_999')   // 返回 999
   * extractServerNumber(undefined)    // 返回 0
   */
  static extractServerNumber(serverId?: string): number {
    if (!serverId || typeof serverId !== 'string') {
      return 0; // 默认区服编号
    }
    
    // 提取末尾的数字
    const match = serverId.match(/(\d+)$/);
    if (match) {
      const serverNumber = parseInt(match[1]);
      
      // 验证区服编号范围（避免端口溢出）
      if (serverNumber > 999) {
        throw new Error(`区服编号 ${serverNumber} 过大，最大支持999`);
      }
      
      return serverNumber;
    }
    
    // 如果没有数字，返回0
    return 0;
  }

  /**
   * 检测端口是否可用
   * 
   * @param port 要检测的端口号
   * @returns Promise<boolean> 端口是否可用
   */
  static async validatePortAvailability(port: number): Promise<boolean> {
    // 验证端口号范围
    if (port < 1 || port > 65535) {
      return false;
    }
    
    return new Promise((resolve) => {
      const server = net.createServer();
      
      // 设置超时
      const timeout = setTimeout(() => {
        server.close();
        resolve(false);
      }, 5000); // 5秒超时
      
      server.listen(port, () => {
        clearTimeout(timeout);
        server.close(() => {
          resolve(true); // 端口可用
        });
      });
      
      server.on('error', (err: any) => {
        clearTimeout(timeout);
        if (err.code === 'EADDRINUSE') {
          resolve(false); // 端口被占用
        } else {
          resolve(false); // 其他错误，认为不可用
        }
      });
    });
  }

  /**
   * 获取服务的端口范围
   * 
   * @param serviceName 服务名称
   * @returns 端口范围对象
   */
  static getPortRange(serviceName: string): { min: number; max: number } {
    const basePort = this.getBasePort(serviceName);
    const rangeSize = parseInt(process.env.PORT_RANGE_SIZE || '1000');
    
    return {
      min: basePort,
      max: basePort + rangeSize - 1,
    };
  }

  /**
   * 获取服务的完整URL
   * 
   * @param serviceName 服务名称
   * @param serverId 区服ID（可选）
   * @param instanceId 实例ID（可选）
   * @param protocol 协议（默认：http）
   * @param host 主机（默认：localhost）
   * @returns 完整的服务URL
   */
  static getServiceUrl(
    serviceName: string,
    serverId?: string,
    instanceId: number = 0,
    protocol: 'http' | 'https' | 'ws' | 'wss' = 'http',
    host: string = 'localhost'
  ): string {
    const port = this.calculatePort(serviceName, serverId, instanceId);
    return `${protocol}://${host}:${port}`;
  }

  /**
   * 批量验证多个端口的可用性
   * 
   * @param ports 端口数组
   * @returns Promise<Map<number, boolean>> 端口可用性映射
   */
  static async validateMultiplePorts(ports: number[]): Promise<Map<number, boolean>> {
    const results = new Map<number, boolean>();
    
    const promises = ports.map(async (port) => {
      const isAvailable = await this.validatePortAvailability(port);
      results.set(port, isAvailable);
    });
    
    await Promise.all(promises);
    return results;
  }

  /**
   * 查找下一个可用端口
   * 
   * @param startPort 起始端口
   * @param maxAttempts 最大尝试次数（默认：10）
   * @returns Promise<number | null> 可用端口或null
   */
  static async findNextAvailablePort(startPort: number, maxAttempts: number = 10): Promise<number | null> {
    for (let i = 0; i < maxAttempts; i++) {
      const port = startPort + i;
      const isAvailable = await this.validatePortAvailability(port);
      
      if (isAvailable) {
        return port;
      }
    }
    
    return null; // 未找到可用端口
  }

  /**
   * 验证端口配置
   * 
   * @returns 验证结果
   */
  static validatePortConfiguration(): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // 检查基础端口配置
    Object.keys(this.SERVICE_BASE_PORTS).forEach(serviceName => {
      try {
        const basePort = this.getBasePort(serviceName);
        
        // 检查端口范围
        if (basePort < 1024) {
          warnings.push(`${serviceName}: 基础端口 ${basePort} 小于1024，可能需要管理员权限`);
        }
        
        if (basePort > 60000) {
          warnings.push(`${serviceName}: 基础端口 ${basePort} 过大，可能与系统端口冲突`);
        }
        
      } catch (error) {
        errors.push(`${serviceName}: ${error.message}`);
      }
    });
    
    // 检查端口冲突
    const portConflicts = this.detectPortConflicts();
    if (portConflicts.length > 0) {
      errors.push(`检测到端口冲突: ${portConflicts.join(', ')}`);
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 检测端口冲突
   * 
   * @returns 冲突的端口列表
   */
  private static detectPortConflicts(): number[] {
    const usedPorts = new Set<number>();
    const conflicts: number[] = [];
    
    const services = Object.keys(this.SERVICE_BASE_PORTS);
    const testServers = ['server_001', 'server_002', 'server_003'];
    const testInstances = [0, 1, 2];
    
    services.forEach(serviceName => {
      testServers.forEach(serverId => {
        testInstances.forEach(instanceId => {
          try {
            const port = this.calculatePort(serviceName, serverId, instanceId);
            
            if (usedPorts.has(port)) {
              conflicts.push(port);
            } else {
              usedPorts.add(port);
            }
          } catch (error) {
            // 忽略计算错误
          }
        });
      });
    });
    
    return conflicts;
  }
}
