import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@app/common';
import { ScoutService } from './scout.service';

/**
 * 球探系统控制器
 * 基于old项目的球探相关功能
 */
@Controller()
export class ScoutController {
  private readonly logger = new Logger(ScoutController.name);

  constructor(private readonly scoutService: ScoutService) {}

  /**
   * 获取球探包信息
   * 对应old项目: getScoutPackInfo
   */
  @MessagePattern('scout.getPackInfo')
  @Cacheable({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getScoutPackInfo(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`获取球探包信息: ${payload.characterId}`);
    const scoutPack = await this.scoutService.getScoutPackInfo(payload.characterId);
    return {
      code: 0,
      message: '获取成功',
      data: scoutPack,
    };
  }

  /**
   * 删除球探包中的球员
   * 对应old项目: delScoutPackHero（支持批量删除）
   */
  @MessagePattern('scout.deletePackHero')
  @CacheEvict({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deleteScoutPackHero(@Payload() payload: { characterId: string; index: number[]; serverId?: string }) {
    this.logger.log(`删除球探包球员: ${payload.characterId}, 索引: ${JSON.stringify(payload.index)}`);
    const result = await this.scoutService.deleteScoutPackHero(payload.characterId, payload.index);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 签约球探球员
   * 对应old项目: signScoutHero（使用resId而不是index）
   */
  @MessagePattern('scout.signHero')
  @CacheEvict({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async signScoutHero(@Payload() payload: { characterId: string; resId: number; serverId?: string }) {
    this.logger.log(`签约球探球员: ${payload.characterId}, 配置ID: ${payload.resId}`);
    const result = await this.scoutService.signScoutHero(payload.characterId, payload.resId);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 球探探索（核心功能）
   * 对应old项目: getScoutReward
   */
  @MessagePattern('scout.explore')
  @CacheEvict({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getScoutReward(@Payload() payload: { characterId: string; type: number; serverId?: string }) {
    this.logger.log(`球探探索: ${payload.characterId}, 类型: ${payload.type}`);
    const result = await this.scoutService.getScoutReward(payload.characterId, payload.type);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 球探搜索
   * 基于old项目扩展的球探搜索功能
   */
  @MessagePattern('scout.search')
  async scoutSearch(@Payload() payload: { 
    characterId: string; 
    scoutType: string; 
    targetQuality?: string;
    serverId?: string;
  }) {
    this.logger.log(`球探搜索: ${payload.characterId}, 类型: ${payload.scoutType}`);
    const result = await this.scoutService.scoutSearch(payload.characterId, payload.scoutType, payload.targetQuality);
    return {
      code: 0,
      message: '搜索完成',
      data: result,
    };
  }

  /**
   * 球探RP值兑换
   * 基于old项目Scout.exchangeScout方法
   */
  @MessagePattern('scout.exchangeScout')
  async exchangeScout(@Payload() payload: {
    characterId: string;
    type?: number;
    serverId?: string;
  }) {
    this.logger.log(`球探RP值兑换: ${payload.characterId}, 类型: ${payload.type || 3}`);
    const result = await this.scoutService.exchangeScout(payload.characterId, payload.type || 3);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 购买球探体力
   * 基于old项目的体力购买机制
   */
  @MessagePattern('scout.buyEnergy')
  async buyScoutEnergy(@Payload() payload: {
    characterId: string;
    amount?: number;
    serverId?: string;
  }) {
    this.logger.log(`购买球探体力: ${payload.characterId}, 数量: ${payload.amount || 50}`);
    const result = await this.scoutService.buyScoutEnergy(payload.characterId, payload.amount || 50);
    return result; // 直接返回结果，因为已经包含了code和message
  }

  /**
   * 手动恢复球探体力
   * 基于old项目的体力恢复机制
   */
  @MessagePattern('scout.recoverEnergy')
  async recoverScoutEnergy(@Payload() payload: {
    characterId: string;
    serverId?: string;
  }) {
    this.logger.log(`手动恢复球探体力: ${payload.characterId}`);
    const result = await this.scoutService.recoverScoutEnergy(payload.characterId);
    return result; // 直接返回结果，因为已经包含了code和message
  }

}
