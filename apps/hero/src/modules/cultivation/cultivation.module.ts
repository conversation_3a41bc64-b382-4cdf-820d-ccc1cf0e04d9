import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CultivationController } from './cultivation.controller';
import { CultivationService } from './cultivation.service';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { Hero, HeroSchema } from '@hero/common/schemas/hero.schema';
import { MicroserviceKitModule } from '@common/microservice-kit';

/**
 * 球员养成模块
 * 处理球员养成、突破、升星等功能
 *
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 */
@Module({
  imports: [
    // 注册Hero Schema（HeroRepository需要）
    MongooseModule.forFeature([
      { name: Hero.name, schema: HeroSchema },
    ]),
    // 导入微服务通信模块
    MicroserviceKitModule,
  ],
  controllers: [CultivationController],
  providers: [
    CultivationService,
    HeroRepository, // 共享的Hero数据访问层
  ],
  exports: [CultivationService],
})
export class CultivationModule {}
