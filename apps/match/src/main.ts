import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { ServiceConfigurationBuilder } from '@libs/service-mesh';
import { AppModule } from './app.module';

/**
 * 比赛系统微服务启动入口
 *
 * 服务信息：
 * - 服务名称: match
 * - 端口: 3005 (从环境变量获取)
 * - 协议: Redis (微服务间通信)
 * - HTTP端口: 用于健康检查和Swagger文档
 *
 * 启动步骤：
 * 1. 创建应用实例
 * 2. 获取配置服务
 * 3. 设置全局前缀
 * 4. 配置Swagger文档
 * 5. 启动HTTP服务器
 */
async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // 🎯 1. 创建应用实例
  const app = await NestFactory.create(AppModule);

  // 2. 获取配置服务并计算配置（新架构）
  const configService = app.get(ConfigService);
  const serviceConfig = await ServiceConfigurationBuilder.build('match', configService);

  logger.log(`🎯 服务配置获取完成:`);
  logger.log(`   📋 服务名称: ${serviceConfig.serviceName}`);
  logger.log(`   🏰 区服ID: ${serviceConfig.serverId}`);
  logger.log(`   🔢 实例序号: ${serviceConfig.instanceIndex}`);
  logger.log(`   🌐 监听地址: ${serviceConfig.host}:${serviceConfig.port}`);
  logger.log(`   🔧 端口计算: ${serviceConfig.portCalculated ? '动态计算' : '固定配置'}`);
  logger.log(`   🌍 运行环境: ${serviceConfig.serviceConfig.environment}`);

  // ✅ 动态端口计算
  let port: number;
  let portCalculated = false;

  try {
    // 优先使用动态端口计算
    port = PortManager.calculatePort('match', serverId, instanceIndex);
    portCalculated = true;
    logger.log(`🔢 动态计算端口: match@${serverId || 'default'} 实例${instanceIndex} -> ${serviceConfig.port}`);
  } catch (error) {
    // 降级到固定端口配置
    port = configService.get('MATCH_PORT', 3005);
    portCalculated = false;
    logger.warn(`⚠️ 端口计算失败，使用固定端口: ${serviceConfig.port}`);
    logger.warn(`   错误信息: ${error.message}`);
  }

  // ✅ 验证端口可用性
  try {
    const isPortAvailable = await PortManager.validatePortAvailability(port);
    if (!isPortAvailable) {
      logger.error(`❌ 端口 ${serviceConfig.port} 不可用`);

      // 尝试查找下一个可用端口
      const nextPort = await PortManager.findNextAvailablePort(serviceConfig.port, 10);
      if (nextPort) {
        logger.log(`🔄 自动切换到可用端口: ${nextPort}`);
        port = nextPort;
      } else {
        throw new Error(`无法找到可用端口，起始端口: ${serviceConfig.port}`);
      }
    } else {
      logger.log(`✅ 端口验证通过: ${serviceConfig.port}`);
    }
  } catch (error) {
    logger.error(`❌ 端口验证失败: ${error.message}`);
    // 继续使用原端口，让系统自然报错
  }

  // 3. 配置Swagger文档
  if (serviceConfig.environment !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('足球比赛服务 API')
      .setDescription('比赛系统微服务API文档')
      .setVersion('1.0.0')
      .addTag('match', '比赛系统')
      .addTag('league', '联赛系统')
      .addTag('business', '商业赛系统')
      .addTag('trophy', '杯赛系统')
      .addTag('tournament', '锦标赛系统')
      .addTag('battle', '战斗引擎')
      .addTag('ranking', '排名系统')
      .addTag('health', '健康检查')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document);

    logger.log(`📚 Swagger文档已启用: http://localhost:${serviceConfig.port}/docs`);
  }

  // 4. 使用 ConfigService 获取微服务配置
  const microserviceConfig = configService.get('microserviceKit');
  const serviceConfig = microserviceConfig?.services[MICROSERVICE_NAMES.MATCH_SERVICE];

  if (serviceConfig) {
    const microserviceOptions = {
      transport: serviceConfig.transserviceConfig.port,
      options: serviceConfig.options,
    };

    // 调试日志：显示微服务配置
    logger.log(`🔗 微服务传输层配置:`);
    logger.log(`📡 传输方式: ${serviceConfig.transport}`);
    logger.log(`🔌 Redis 主机: ${serviceConfig.options.host}`);
    logger.log(`🔌 Redis 端口: ${serviceConfig.options.port}`);
    logger.log(`🗄️  Redis 数据库: ${serviceConfig.options.db}`);
    logger.log(`🔑 Redis 密码: ${serviceConfig.options.password ? '***' : '未设置'}`);

    // 连接微服务传输层
    app.connectMicroservice(microserviceOptions);

    // 启动所有微服务
    await app.startAllMicroservices();
    logger.log(`🔗 微服务传输层已启动 (${serviceConfig.transport})`);
  } else {
    logger.error(`❌ 未找到微服务配置: ${MICROSERVICE_NAMES.MATCH_SERVICE}`);
    logger.error(`📋 可用配置: ${Object.keys(microserviceConfig?.services || {}).join(', ')}`);
  }

  // 5. 启动HTTP服务器
  await app.listen(serviceConfig.port, '0.0.0.0');

  // ✅ 增强的启动日志
  logger.log(`🚀 Match服务启动成功!`);
  logger.log(`=`.repeat(60));
  logger.log(`📋 服务信息:`);
  logger.log(`   🏷️  服务名称: match`);
  logger.log(`   🌐 监听地址: 0.0.0.0:${serviceConfig.port}`);
  logger.log(`   🔗 健康检查: http://localhost:${serviceConfig.port}/health`);

  logger.log(`📊 端口信息:`);
  logger.log(`   🔢 使用端口: ${serviceConfig.port}`);
  logger.log(`   📈 分配策略: ${portCalculated ? '动态计算' : '固定配置'}`);
  if (portCalculated) {
    try {
      const basePort = PortManager.getBasePort('match');
      logger.log(`   📊 基础端口: ${basePort}`);
      logger.log(`   🏷️  区服ID: ${serverId || 'default'}`);
      logger.log(`   🔢 实例ID: ${instanceIndex}`);
      logger.log(`   🧮 计算公式: ${basePort} + (${PortManager.extractServerNumber(serverId)} * 10) + ${instanceIndex} = ${serviceConfig.port}`);
    } catch (error) {
      logger.warn(`   ⚠️ 无法获取端口详情: ${error.message}`);
    }
  }

  logger.log(`🔧 运行环境:`);
  logger.log(`   🌍 环境: ${serviceConfig.environment}`);
  logger.log(`   📡 服务注册: 自动启用`);

  if (serviceConfig.environment !== 'production') {
    logger.log(`📚 开发工具:`);
    logger.log(`   📖 API文档: http://localhost:${serviceConfig.port}/docs`);
    logger.log(`   🔍 调试模式: 已启用`);
  }

  logger.log(`=`.repeat(60));
  logger.log(`✅ Match服务已完全启动并准备接收请求`);
}

bootstrap().catch((error) => {
  console.error('❌ 比赛服务启动失败:', error);
  process.exit(1);
});
