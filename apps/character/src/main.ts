import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { ServiceConfigurationBuilder } from '@libs/service-mesh';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // 🎯 1. 预计算服务配置（新架构核心）
  const configService = new ConfigService();
  const serviceConfig = await ServiceConfigurationBuilder
    .forService('character')
    .build(configService);

  logger.log(`🎯 使用预计算配置:`);
  logger.log(`   📋 服务名称: ${serviceConfig.serviceName}`);
  logger.log(`   🏰 区服ID: ${serviceConfig.serverId}`);
  logger.log(`   🔢 实例序号: ${serviceConfig.instanceIndex}`);
  logger.log(`   🌐 监听地址: ${serviceConfig.host}:${serviceConfig.port}`);
  logger.log(`   🔧 端口计算: ${serviceConfig.portCalculated ? '动态计算' : '固定配置'}`);
  logger.log(`   🌍 运行环境: ${serviceConfig.environment}`);

  // 2. 创建应用实例
  const app = await NestFactory.create(AppModule);

  // ✅ 验证端口可用性
  try {
    const isPortAvailable = await PortManager.validatePortAvailability(port);
    if (!isPortAvailable) {
      logger.error(`❌ 端口 ${port} 不可用`);

      // 尝试查找下一个可用端口
      const nextPort = await PortManager.findNextAvailablePort(port, 10);
      if (nextPort) {
        logger.log(`🔄 自动切换到可用端口: ${nextPort}`);
        port = nextPort;
      } else {
        throw new Error(`无法找到可用端口，起始端口: ${port}`);
      }
    } else {
      logger.log(`✅ 端口验证通过: ${port}`);
    }
  } catch (error) {
    logger.error(`❌ 端口验证失败: ${error.message}`);
    // 继续使用原端口，让系统自然报错
  }

  // 3. Swagger API 文档
  if (environment !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('足球角色服务')
      .setDescription('提供角色管理、阵容配置、物品管理和战术设置的微服务API')
      .setVersion('1.0.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-API-Key',
          in: 'header',
          description: 'API Key for service-to-service authentication',
        },
        'API-Key',
      )
      .addTag('角色', '角色管理相关接口')
      .addTag('阵容', '阵容配置相关接口')
      .addTag('物品', '物品管理相关接口')
      .addTag('背包', '背包系统相关接口')
      .addTag('战术', '战术设置相关接口')
      .addTag('健康检查', '服务健康状态接口')
      .addServer(`http://localhost:${port}`, '本地开发环境')
      .addServer('https://api-dev.yourgame.com', '开发环境')
      .addServer('https://api.yourgame.com', '生产环境')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
      customSiteTitle: '足球角色服务 API',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #1976d2 }
      `,
    });

    logger.log(`📚 API文档已启用: http://localhost:${port}/docs`);
  }

  // 4. 优雅关闭
  process.on('SIGTERM', async () => {
    logger.log('🔄 收到SIGTERM信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('🔄 收到SIGINT信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  // 5. 使用 ConfigService 获取微服务配置
  const microserviceConfig = configService.get('microserviceKit');
  const serviceConfig = microserviceConfig?.services[MICROSERVICE_NAMES.CHARACTER_SERVICE];

  if (serviceConfig) {
    const microserviceOptions = {
      transport: serviceConfig.transport,
      options: serviceConfig.options,
    };

    // 调试日志：显示微服务配置
    logger.log(`🔍 微服务配置调试:`);
    logger.log(`📡 传输方式: ${serviceConfig.transport}`);
    logger.log(`🏠 Redis 主机: ${serviceConfig.options.host}`);
    logger.log(`🔌 Redis 端口: ${serviceConfig.options.port}`);
    logger.log(`🗄️  Redis 数据库: ${serviceConfig.options.db}`);
    logger.log(`🔑 Redis 密码: ${serviceConfig.options.password ? '***' : '未设置'}`);

    // 连接微服务传输层
    app.connectMicroservice(microserviceOptions);

    // 启动所有微服务
    await app.startAllMicroservices();
    logger.log(`🔗 微服务传输层已启动 (${serviceConfig.transport})`);
  } else {
    logger.error(`❌ 未找到微服务配置: ${MICROSERVICE_NAMES.CHARACTER_SERVICE}`);
    logger.error(`📋 可用配置: ${Object.keys(microserviceConfig?.services || {}).join(', ')}`);
  }

  // 6. 启动HTTP服务
  await app.listen(port, '0.0.0.0');

  // ✅ 增强的启动日志
  logger.log(`🚀 Character服务启动成功!`);
  logger.log(`=`.repeat(60));
  logger.log(`📋 服务信息:`);
  logger.log(`   🏷️  服务名称: character`);
  logger.log(`   🌐 监听地址: 0.0.0.0:${port}`);
  logger.log(`   🔗 健康检查: http://localhost:${port}/health`);

  logger.log(`📊 端口信息:`);
  logger.log(`   🔢 使用端口: ${port}`);
  logger.log(`   📈 分配策略: ${portCalculated ? '动态计算' : '固定配置'}`);
  if (portCalculated) {
    try {
      const basePort = PortManager.getBasePort('character');
      logger.log(`   📊 基础端口: ${basePort}`);
      logger.log(`   🏷️  区服ID: ${serverId || 'default'}`);
      logger.log(`   🔢 实例ID: ${instanceIndex}`);
      logger.log(`   🧮 计算公式: ${basePort} + (${PortManager.extractServerNumber(serverId)} * 10) + ${instanceIndex} = ${port}`);
    } catch (error) {
      logger.warn(`   ⚠️ 无法获取端口详情: ${error.message}`);
    }
  }

  logger.log(`🔧 运行环境:`);
  logger.log(`   🌍 环境: ${environment}`);
  logger.log(`   🔗 微服务: Redis传输层`);
  logger.log(`   📡 服务注册: 自动启用`);

  if (environment !== 'production') {
    logger.log(`📚 开发工具:`);
    logger.log(`   📖 API文档: http://localhost:${port}/docs`);
    logger.log(`   🔍 调试模式: 已启用`);
  }

  logger.log(`=`.repeat(60));
  logger.log(`✅ Character服务已完全启动并准备接收请求`);
}

// 启动应用
bootstrap().catch((error) => {
  console.error('❌ 应用启动失败:', error);
  process.exit(1);
});
