import { Controller, Post, Get, Delete, Body, Param, Query, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { 
  ServerAwareRegistryService, 
  ServerAwareLoadBalancerService,
  ServiceRegistrationRequest 
} from '@libs/service-mesh';

/**
 * 服务注册管理API控制器
 * 
 * 提供服务实例的注册、注销、查询和管理功能
 * 主要用于：
 * - 微服务实例自动注册
 * - 管理员手动管理服务实例
 * - 监控和调试服务发现状态
 */
@ApiTags('Admin - Service Registry')
@Controller('admin/service-registry')
export class ServiceRegistryController {
  private readonly logger = new Logger(ServiceRegistryController.name);

  constructor(
    private readonly serviceRegistry: ServerAwareRegistryService,
    private readonly loadBalancer: ServerAwareLoadBalancerService,
  ) {}

  /**
   * 注册服务实例
   */
  @Post('register')
  @ApiOperation({ summary: '注册服务实例' })
  @ApiResponse({ status: 201, description: '注册成功' })
  @ApiResponse({ status: 400, description: '注册参数错误' })
  async registerInstance(@Body() request: ServiceRegistrationRequest) {
    this.logger.log(`📝 注册服务实例请求: ${request.serviceName}@${request.serverId} 实例${request.instanceIndex || 0}`);

    try {
      const instanceId = this.serviceRegistry.registerInstance(request);

      this.logger.log(`✅ 服务实例注册成功: ${instanceId}`);

      return {
        success: true,
        data: {
          instanceId,
          serviceName: request.serviceName,
          serverId: request.serverId,
          instanceIndex: request.instanceIndex || 0,
          registeredAt: new Date().toISOString(),
        },
        message: '服务实例注册成功',
      };
    } catch (error) {
      this.logger.error(`❌ 服务实例注册失败: ${request.serviceName}@${request.serverId}`, error);
      
      return {
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 注销服务实例
   */
  @Post('unregister')
  @ApiOperation({ summary: '注销服务实例' })
  @ApiResponse({ status: 200, description: '注销成功' })
  async unregisterInstance(@Body() body: { serviceName: string; serverId: string; instanceName: string }) {
    const { serviceName, serverId, instanceName } = body;
    
    this.logger.log(`🗑️ 注销服务实例请求: ${instanceName} (${serviceName}@${serverId})`);
    
    try {
      const success = this.serviceRegistry.unregisterInstance(serviceName, serverId, instanceName);
      
      if (success) {
        this.logger.log(`✅ 服务实例注销成功: ${instanceName}`);
        
        return {
          success: true,
          message: '服务实例注销成功',
        };
      } else {
        this.logger.warn(`⚠️ 服务实例不存在: ${instanceName}`);
        
        return {
          success: false,
          error: {
            code: 'INSTANCE_NOT_FOUND',
            message: '服务实例不存在',
          },
        };
      }
    } catch (error) {
      this.logger.error(`❌ 服务实例注销失败: ${instanceName}`, error);
      
      return {
        success: false,
        error: {
          code: 'UNREGISTRATION_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取服务实例列表
   */
  @Get('instances/:serviceName/:serverId')
  @ApiOperation({ summary: '获取指定区服的服务实例列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getInstances(
    @Param('serviceName') serviceName: string,
    @Param('serverId') serverId: string,
    @Query('healthyOnly') healthyOnly?: boolean
  ) {
    this.logger.debug(`📋 获取服务实例列表: ${serviceName}@${serverId}, healthyOnly=${healthyOnly}`);
    
    try {
      const instances = healthyOnly === true
        ? await this.serviceRegistry.getHealthyInstances(serviceName, serverId)
        : this.serviceRegistry.getInstances(serviceName, serverId);

      return {
        success: true,
        data: {
          serviceName,
          serverId,
          totalInstances: instances.length,
          instances: instances.map(instance => ({
            id: instance.id,
            instanceName: instance.instanceName,
            host: instance.host,
            port: instance.port,
            healthy: instance.healthy,
            weight: instance.weight,
            connections: instance.connections,
            responseTime: instance.responseTime,
            lastHealthCheck: instance.lastHealthCheck,
            registeredAt: instance.registeredAt,
            metadata: instance.metadata,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`❌ 获取服务实例列表失败: ${serviceName}@${serverId}`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_INSTANCES_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取所有服务的实例列表
   */
  @Get('instances/:serviceName')
  @ApiOperation({ summary: '获取服务在所有区服的实例列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAllServiceInstances(@Param('serviceName') serviceName: string) {
    this.logger.debug(`📋 获取服务所有实例: ${serviceName}`);
    
    try {
      const allInstances = this.serviceRegistry.getAllInstances(serviceName);
      const servers: any = {};
      
      for (const [serverId, instances] of allInstances) {
        servers[serverId] = {
          totalInstances: instances.length,
          healthyInstances: instances.filter(inst => inst.healthy).length,
          instances: instances.map(instance => ({
            id: instance.id,
            instanceName: instance.instanceName,
            host: instance.host,
            port: instance.port,
            healthy: instance.healthy,
            connections: instance.connections,
            responseTime: instance.responseTime,
            lastHealthCheck: instance.lastHealthCheck,
          })),
        };
      }
      
      return {
        success: true,
        data: {
          serviceName,
          totalServers: allInstances.size,
          servers,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 获取服务所有实例失败: ${serviceName}`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_ALL_INSTANCES_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取可用区服列表
   */
  @Get('servers/:serviceName')
  @ApiOperation({ summary: '获取服务的可用区服列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAvailableServers(@Param('serviceName') serviceName: string) {
    this.logger.debug(`🌐 获取可用区服列表: ${serviceName}`);
    
    try {
      const availableServers = this.serviceRegistry.getAvailableServers(serviceName);
      
      return {
        success: true,
        data: {
          serviceName,
          availableServers,
          totalServers: availableServers.length,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 获取可用区服列表失败: ${serviceName}`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_SERVERS_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 更新实例健康状态
   */
  @Post('health/:serviceName/:serverId/:instanceName')
  @ApiOperation({ summary: '更新实例健康状态' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateInstanceHealth(
    @Param('serviceName') serviceName: string,
    @Param('serverId') serverId: string,
    @Param('instanceName') instanceName: string,
    @Body() body: { healthy: boolean; responseTime?: number }
  ) {
    const { healthy, responseTime } = body;
    
    this.logger.debug(`🏥 更新实例健康状态: ${instanceName} (${serviceName}@${serverId}) -> ${healthy}`);
    
    try {
      const success = this.serviceRegistry.updateInstanceHealth(
        serviceName,
        serverId,
        instanceName,
        healthy,
        responseTime
      );
      
      if (success) {
        return {
          success: true,
          message: '实例健康状态更新成功',
        };
      } else {
        return {
          success: false,
          error: {
            code: 'INSTANCE_NOT_FOUND',
            message: '服务实例不存在',
          },
        };
      }
    } catch (error) {
      this.logger.error(`❌ 更新实例健康状态失败: ${instanceName}`, error);
      
      return {
        success: false,
        error: {
          code: 'UPDATE_HEALTH_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取服务统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取服务注册统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getServiceStats(@Query('serviceName') serviceName?: string) {
    this.logger.debug(`📊 获取服务统计信息: ${serviceName || 'all'}`);
    
    try {
      const stats = this.serviceRegistry.getServiceStats(serviceName);
      
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`❌ 获取服务统计信息失败`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_STATS_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取负载均衡统计信息
   */
  @Get('load-balancer/stats')
  @ApiOperation({ summary: '获取负载均衡统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getLoadBalancerStats(
    @Query('serviceName') serviceName?: string,
    @Query('serverId') serverId?: string
  ) {
    this.logger.debug(`⚖️ 获取负载均衡统计信息: ${serviceName || 'all'}@${serverId || 'all'}`);
    
    try {
      // 🚀 从注册中心获取实例，然后获取负载均衡统计
      const instances = await this.serviceRegistry.getHealthyInstances(serviceName, serverId);
      const stats = {
        serviceName,
        serverId,
        totalInstances: instances.length,
        healthyInstances: instances.filter(inst => inst.healthy).length,
        availableStrategies: this.loadBalancer.getAvailableStrategies(),
        instances: instances.map(inst => ({
          instanceName: inst.instanceName,
          healthy: inst.healthy,
          connections: inst.connections,
          responseTime: inst.responseTime,
          weight: inst.weight,
        }))
      };
      
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`❌ 获取负载均衡统计信息失败`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_LB_STATS_FAILED',
          message: error.message,
        },
      };
    }
  }
}
